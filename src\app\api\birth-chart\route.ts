import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { calculateEnhancedBirthChart, BirthDetails } from '@/lib/astrology';
import { generateHoroscopeInterpretations } from '@/lib/horoscope-interpretations';
import { generateBirthChartTranslations } from '@/lib/birth-chart-translations';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log('🔮 Calculating birth chart for user (updated):', userId);

    // Get user data
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has required birth data
    if (!user.birthDate || !user.birthTime || !user.birthLatitude || !user.birthLongitude) {
      return NextResponse.json(
        { success: false, error: 'Incomplete birth data. Birth date, time, and location are required.' },
        { status: 400 }
      );
    }

    // Prepare birth details
    const birthDetails: BirthDetails = {
      birthDate: new Date(user.birthDate),
      birthTime: user.birthTime,
      birthPlace: user.birthPlace || 'Unknown',
      latitude: user.birthLatitude,
      longitude: user.birthLongitude
    };

    console.log('📊 Birth details:', birthDetails);

    // Calculate enhanced birth chart with Vedic calculations
    const chartData = await calculateEnhancedBirthChart(birthDetails);
    console.log('✅ Enhanced Vedic chart calculated, generating interpretations...');

    // Generate interpretations
    const interpretations = generateHoroscopeInterpretations(chartData);
    console.log('✅ Interpretations generated');

    // Generate translations for the interpretations
    console.log('🌐 Generating translations for birth chart readings...');
    const translations = await generateBirthChartTranslations(interpretations);
    console.log('✅ Translations generated');

    // Determine timezone (simplified - you might want to use a proper timezone library)
    const timezone = getTimezoneFromCoordinates(user.birthLatitude, user.birthLongitude);

    // Create birth chart record
    const birthChart = await prisma.birthChart.upsert({
      where: { userId: userId },
      update: {
        birthDateTime: new Date(`${user.birthDate.toISOString().split('T')[0]}T${user.birthTime}:00`),
        birthPlace: user.birthPlace || 'Unknown',
        birthLatitude: user.birthLatitude,
        birthLongitude: user.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets as any,
        housePositions: chartData.houses as any,
        aspects: chartData.aspects as any,
        nakshatras: chartData.nakshatras as any,
        dashas: chartData.dashas as any,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        // Enhanced Vedic Chart Data
        lagnaChart: chartData.lagnaChart as any,
        navamsaChart: chartData.navamsaChart as any,
        chandraChart: chartData.chandraChart as any,
        karakTable: chartData.karakTable as any,
        avasthaTable: chartData.avasthaTable as any,
        planetaryDetails: chartData.planetaryDetails as any,
        vimshottariDasha: chartData.vimshottariDasha as any,
        ashtakavarga: chartData.ashtakavarga as any,
        // Additional Vedic Calculations
        panchang: chartData.panchang as any,
        doshaAnalysis: chartData.doshaAnalysis as any,
        yogaAnalysis: chartData.yogaAnalysis as any,
        planetaryStrengths: chartData.planetaryStrengths as any,
        divisionalCharts: chartData.divisionalCharts as any,
        generalReading: interpretations.generalReading,
        strengthsWeaknesses: interpretations.strengthsWeaknesses,
        careerGuidance: interpretations.careerGuidance,
        relationshipGuidance: interpretations.relationshipGuidance,
        healthGuidance: interpretations.healthGuidance,
        readingsEn: translations.en as any,
        readingsSi: translations.si as any
      },
      create: {
        userId: userId,
        birthDateTime: new Date(`${user.birthDate.toISOString().split('T')[0]}T${user.birthTime}:00`),
        birthPlace: user.birthPlace || 'Unknown',
        birthLatitude: user.birthLatitude,
        birthLongitude: user.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets as any,
        housePositions: chartData.houses as any,
        aspects: chartData.aspects as any,
        nakshatras: chartData.nakshatras as any,
        dashas: chartData.dashas as any,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        // Enhanced Vedic Chart Data
        lagnaChart: chartData.lagnaChart as any,
        navamsaChart: chartData.navamsaChart as any,
        chandraChart: chartData.chandraChart as any,
        karakTable: chartData.karakTable as any,
        avasthaTable: chartData.avasthaTable as any,
        planetaryDetails: chartData.planetaryDetails as any,
        vimshottariDasha: chartData.vimshottariDasha as any,
        ashtakavarga: chartData.ashtakavarga as any,
        // Additional Vedic Calculations
        panchang: chartData.panchang as any,
        doshaAnalysis: chartData.doshaAnalysis as any,
        yogaAnalysis: chartData.yogaAnalysis as any,
        planetaryStrengths: chartData.planetaryStrengths as any,
        divisionalCharts: chartData.divisionalCharts as any,
        generalReading: interpretations.generalReading,
        strengthsWeaknesses: interpretations.strengthsWeaknesses,
        careerGuidance: interpretations.careerGuidance,
        relationshipGuidance: interpretations.relationshipGuidance,
        healthGuidance: interpretations.healthGuidance,
        readingsEn: translations.en as any,
        readingsSi: translations.si as any
      }
    });

    console.log('✅ Birth chart saved to database');

    return NextResponse.json({
      success: true,
      data: {
        birthChart: {
          id: birthChart.id,
          ascendant: birthChart.ascendant,
          moonSign: birthChart.moonSign,
          sunSign: birthChart.sunSign,
          generalReading: birthChart.generalReading,
          strengthsWeaknesses: birthChart.strengthsWeaknesses,
          careerGuidance: birthChart.careerGuidance,
          relationshipGuidance: birthChart.relationshipGuidance,
          healthGuidance: birthChart.healthGuidance,
          calculatedAt: birthChart.calculatedAt
        }
      }
    });

  } catch (error) {
    console.error('❌ Error calculating birth chart:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to calculate birth chart' 
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Get existing birth chart
    const birthChart = await prisma.birthChart.findUnique({
      where: { userId: userId },
      include: {
        user: {
          select: {
            name: true,
            birthDate: true,
            birthTime: true,
            birthPlace: true
          }
        }
      }
    });

    if (!birthChart) {
      return NextResponse.json(
        { success: false, error: 'Birth chart not found. Please calculate first.' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        birthChart: {
          id: birthChart.id,
          ascendant: birthChart.ascendant,
          moonSign: birthChart.moonSign,
          sunSign: birthChart.sunSign,
          planetPositions: birthChart.planetPositions,
          housePositions: birthChart.housePositions,
          aspects: birthChart.aspects,
          nakshatras: birthChart.nakshatras,
          dashas: birthChart.dashas,
          // Enhanced Vedic Chart Data
          lagnaChart: birthChart.lagnaChart,
          navamsaChart: birthChart.navamsaChart,
          chandraChart: birthChart.chandraChart,
          karakTable: birthChart.karakTable,
          avasthaTable: birthChart.avasthaTable,
          planetaryDetails: birthChart.planetaryDetails,
          vimshottariDasha: birthChart.vimshottariDasha,
          ashtakavarga: birthChart.ashtakavarga,
          generalReading: birthChart.generalReading,
          strengthsWeaknesses: birthChart.strengthsWeaknesses,
          careerGuidance: birthChart.careerGuidance,
          relationshipGuidance: birthChart.relationshipGuidance,
          healthGuidance: birthChart.healthGuidance,
          readingsEn: birthChart.readingsEn,
          readingsSi: birthChart.readingsSi,
          calculatedAt: birthChart.calculatedAt,
          user: birthChart.user
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fetching birth chart:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch birth chart' 
      },
      { status: 500 }
    );
  }
}

/**
 * Simple timezone detection based on coordinates
 * In a real application, you'd use a proper timezone library
 */
function getTimezoneFromCoordinates(latitude: number, longitude: number): string {
  // Sri Lanka timezone
  if (latitude >= 5.9 && latitude <= 9.9 && longitude >= 79.6 && longitude <= 81.9) {
    return 'Asia/Colombo';
  }
  
  // Simple longitude-based timezone estimation
  const timezoneOffset = Math.round(longitude / 15);
  return `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`;
}
