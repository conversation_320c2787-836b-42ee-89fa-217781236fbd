'use client';

import React from 'react';
import {
  KarakData,
  AvasthaData,
  PlanetaryDetail,
  VimshottariDashaData,
  AshtakavargaData,
  PanchangData,
  DoshaAnalysis,
  YogaAnalysis
} from '@/lib/astrology';
import { useUITranslation } from '@/utils/ui-translations';
import { useLanguage } from '@/hooks/useLanguage';

interface AstrologicalTablesProps {
  karakTable?: KarakData;
  avasthaTable?: AvasthaData;
  planetaryDetails?: PlanetaryDetail[];
  vimshottariDasha?: VimshottariDashaData;
  ashtakavarga?: AshtakavargaData;
  panchang?: PanchangData;
  doshaAnalysis?: DoshaAnalysis;
  yogaAnalysis?: YogaAnalysis;
}

export default function AstrologicalTables({
  karakTable,
  avasthaTable,
  planetaryDetails,
  vimshottariDasha,
  ashtakavarga,
  panchang,
  doshaAnalysis,
  yogaAnalysis
}: AstrologicalTablesProps) {
  const { t } = useUITranslation();
  const { language } = useLanguage();

  // Debug: Log current language and test translations
  console.log('🔍 AstrologicalTables Language Context:', {
    currentLanguage: language,
    testTranslations: {
      libra: t('libra'),
      pisces: t('pisces'),
      aquarius: t('aquarius'),
      chiron: t('chiron'),
      sirius: t('sirius'),
      susupla: t('susupla'),
      vadha: t('vadha')
    }
  });

  // Debug: Log the actual data being passed
  console.log('🔍 AstrologicalTables Data:', {
    planetaryDetails: planetaryDetails?.slice(0, 3), // First 3 entries
    avasthaTable: avasthaTable ? Object.keys(avasthaTable).slice(0, 3) : null, // First 3 keys
    karakTable: karakTable ? Object.keys(karakTable).slice(0, 3) : null
  });

  // Function to translate planet names
  const translatePlanet = (planet: string): string => {
    if (!planet) return planet;

    // Log every planet translation attempt
    console.log('🪐 Attempting to translate planet:', planet);

    // Direct translation mapping for Sinhala when language is 'si'
    const sinhalaPlanetMap: { [key: string]: string } = {
      // English names
      'Sun': 'සූර්ය',
      'Moon': 'චන්ද්‍ර',
      'Mars': 'අඟහරු',
      'Mercury': 'බුධ',
      'Jupiter': 'ගුරු',
      'Venus': 'ශුක්‍ර',
      'Saturn': 'සෙනසුරු',
      'Rahu': 'රාහු',
      'Ketu': 'කේතු',
      'Uranus': 'යුරේනස්',
      'Neptune': 'නෙප්චූන්',
      'Pluto': 'ප්ලූටෝ',
      'Ascendant': 'ලග්න',
      // Uppercase English names
      'SUN': 'සූර්ය',
      'MOON': 'චන්ද්‍ර',
      'MARS': 'අඟහරු',
      'MERCURY': 'බුධ',
      'JUPITER': 'ගුරු',
      'VENUS': 'ශුක්‍ර',
      'SATURN': 'සෙනසුරු',
      'URANUS': 'යුරේනස්',
      'NEPTUNE': 'නෙප්චූන්',
      'PLUTO': 'ප්ලූටෝ',
      // Sanskrit/Vedic names (from astrology library)
      'Surya': 'සූර්ය',
      'Chandra': 'චන්ද්‍ර',
      'Mangal': 'අඟහරු',
      'Budha': 'බුධ',
      'Guru': 'ගුරු',
      'Shukra': 'ශුක්‍ර',
      'Shani': 'සෙනසුරු',
      // Modern planets - all case variations
      'uranus': 'යුරේනස්',
      'neptune': 'නෙප්චූන්',
      'pluto': 'ප්ලූටෝ',
      'chiron': 'චිරෝන්',
      'sirius': 'සිරියස්',
      'Chiron': 'චිරෝන්',
      'Sirius': 'සිරියස්',
      'CHIRON': 'චිරෝන්',
      'SIRIUS': 'සිරියස්'
    };

    // Server-side debug logging
    if (typeof window === 'undefined') {
      console.log('🪐 [SERVER] Planet Translation Called:', {
        originalPlanet: planet,
        language: language,
        timestamp: new Date().toISOString()
      });
    }

    // Use direct Sinhala mapping if language is Sinhala
    if (language === 'si' && sinhalaPlanetMap[planet]) {
      console.log('🪐 Direct Sinhala planet translation:', {
        originalPlanet: planet,
        translatedPlanet: sinhalaPlanetMap[planet],
        language: language
      });

      // Server-side debug logging for successful translation
      if (typeof window === 'undefined') {
        console.log('🪐 [SERVER] Direct Sinhala Translation Success:', {
          originalPlanet: planet,
          translatedPlanet: sinhalaPlanetMap[planet],
          language: language,
          timestamp: new Date().toISOString()
        });
      }

      return sinhalaPlanetMap[planet];
    }

    // Fallback to t() function for English or if direct mapping fails
    const planetMap: { [key: string]: string } = {
      // English names
      'Sun': t('sun'),
      'Moon': t('moon'),
      'Mars': t('mars'),
      'Mercury': t('mercury'),
      'Jupiter': t('jupiter'),
      'Venus': t('venus'),
      'Saturn': t('saturn'),
      'Rahu': t('rahu'),
      'Ketu': t('ketu'),
      'Uranus': t('uranus'),
      'Neptune': t('neptune'),
      'Pluto': t('pluto'),
      'Ascendant': t('ascendant'),
      // Uppercase English names
      'SUN': t('sun'),
      'MOON': t('moon'),
      'MARS': t('mars'),
      'MERCURY': t('mercury'),
      'JUPITER': t('jupiter'),
      'VENUS': t('venus'),
      'SATURN': t('saturn'),
      // Sanskrit/Vedic names (from astrology library)
      'Surya': t('sun'),
      'Chandra': t('moon'),
      'Mangal': t('mars'),
      'Budha': t('mercury'),
      'Guru': t('jupiter'),
      'Shukra': t('venus'),
      'Shani': t('saturn'),
      // Additional celestial bodies - all case variations
      'chiron': t('chiron'),
      'sirius': t('sirius'),
      'Chiron': t('chiron'),
      'Sirius': t('sirius'),
      'CHIRON': t('chiron'),
      'SIRIUS': t('sirius'),
      // Modern planets - all case variations
      'uranus': t('uranus'),
      'neptune': t('neptune'),
      'pluto': t('pluto'),
      'Uranus': t('uranus'),
      'Neptune': t('neptune'),
      'Pluto': t('pluto'),
      'URANUS': t('uranus'),
      'NEPTUNE': t('neptune'),
      'PLUTO': t('pluto')
    };

    // Debug logging for planet translation
    console.log('🪐 Planet Translation:', {
      originalPlanet: planet,
      translatedPlanet: planetMap[planet] || planet,
      language: language,
      foundMapping: !!planetMap[planet],
      availableKeys: Object.keys(planetMap).slice(0, 10) // Show first 10 keys for debugging
    });

    // Try exact match first
    if (planetMap[planet]) {
      return planetMap[planet];
    }

    // Try trimmed version
    const trimmedPlanet = planet.trim();
    if (planetMap[trimmedPlanet]) {
      return planetMap[trimmedPlanet];
    }

    // Try lowercase version
    const lowerPlanet = planet.toLowerCase();
    if (planetMap[lowerPlanet]) {
      return planetMap[lowerPlanet];
    }

    // Try capitalized version
    const capitalizedPlanet = planet.charAt(0).toUpperCase() + planet.slice(1).toLowerCase();
    if (planetMap[capitalizedPlanet]) {
      return planetMap[capitalizedPlanet];
    }

    // If no translation found, return original
    console.warn('🚨 No translation found for planet:', planet);
    return planet;
  };

  // Function to translate zodiac sign abbreviations
  const translateSignAbbr = (sign: string): string => {
    const signMap: { [key: string]: string } = {
      'Ar': t('ar'),
      'Ta': t('ta'),
      'Ge': t('ge'),
      'Ca': t('ca'),
      'Le': t('le'),
      'Vi': t('vi'),
      'Li': t('li'),
      'Sc': t('sc'),
      'Sa': t('sa'),
      'Cp': t('cp'),
      'Aq': t('aq'),
      'Pi': t('pi'),
      'Total': t('total')
    };
    return signMap[sign] || sign;
  };

  // Function to translate relation status
  const translateRelation = (relation: string): string => {
    const relationMap: { [key: string]: string } = {
      'Own': t('own'),
      'Friendly': t('friendly'),
      'Enemy': t('enemy'),
      'Neutral': t('neutral')
    };
    return relationMap[relation] || relation;
  };

  // Function to translate balance string (e.g., "5 years 3 months" to Sinhala)
  const translateBalance = (balance: string): string => {
    if (!balance) return balance;

    // Replace English words with Sinhala equivalents
    return balance
      .replace(/years?/g, t('years'))
      .replace(/months?/g, t('months'))
      .replace(/days?/g, t('days'));
  };

  // Function to translate Karaka terms
  const translateKaraka = (karaka: string): string => {
    if (!karaka) return karaka;

    // Direct translation mapping for Sinhala when language is 'si'
    const sinhalaKarakMap: { [key: string]: string } = {
      'Atma': 'ආත්මා',
      'Alma': 'ආත්මා', // Alternative spelling
      'Dara': 'දාර',
      'Gnati': 'ඥාති',
      'Matru': 'මාතෘ',
      'Putra': 'පුත්‍ර',
      'Amatya': 'අමාත්‍ය',
      'Bhratru': 'භ්‍රාතෘ'
    };

    // Use direct Sinhala mapping if language is Sinhala
    if (language === 'si' && sinhalaKarakMap[karaka]) {
      return sinhalaKarakMap[karaka];
    }

    const karakaMap: { [key: string]: string } = {
      'Alma': t('alma'),
      'Dara': t('dara'),
      'Gnati': t('gnati'),
      'Matru': t('matru'),
      'Putra': t('putra'),
      'Amatya': t('amatya'),
      'Bhratru': t('bhratru')
    };
    return karakaMap[karaka] || karaka;
  };

  // Function to translate Avastha terms
  const translateAvastha = (avastha: string): string => {
    const avasthaMap: { [key: string]: string } = {
      'Swapna': t('swapna'),
      'Jaagrat': t('jaagrat'),
      'Yuva': t('yuva'),
      'Deena': t('deena'),
      'Kumar': t('kumar'),
      'Bala': t('bala'),
      'Swatha': t('swatha'),
      'Mrat': t('mrat'),
      'Muditha': t('muditha'),
      'Susupla': t('susupla'),
      'Vadha': t('vadha'),
      'Deepta': t('deepta'),
      'Khal': t('khal'),
      'Shant': t('shant'),
      // Handle case variations
      'Susupta': t('susupla'), // Alternative spelling
      'Vradha': t('vadha')     // Alternative spelling
    };

    // Debug logging for avastha translation
    console.log('🔮 Avastha Translation:', {
      originalAvastha: avastha,
      translatedAvastha: avasthaMap[avastha] || avastha,
      language: language,
      foundMapping: !!avasthaMap[avastha]
    });

    // Try exact match first
    if (avasthaMap[avastha]) {
      return avasthaMap[avastha];
    }

    // Try trimmed version
    const trimmedAvastha = avastha.trim();
    if (avasthaMap[trimmedAvastha]) {
      return avasthaMap[trimmedAvastha];
    }

    // Try different case variations
    const variations = [
      avastha.toLowerCase(),
      avastha.toUpperCase(),
      avastha.charAt(0).toUpperCase() + avastha.slice(1).toLowerCase()
    ];

    for (const variation of variations) {
      if (avasthaMap[variation]) {
        console.log('🔮 Found avastha translation with variation:', { original: avastha, variation, translated: avasthaMap[variation] });
        return avasthaMap[variation];
      }
    }

    // If no translation found, return original
    console.warn('🚨 No translation found for avastha:', avastha);
    return avastha;
  };

  // Function to translate zodiac signs
  const translateSign = (sign: string): string => {
    if (!sign) return sign;

    // Direct translation mapping for Sinhala when language is 'si'
    const sinhalaSignMap: { [key: string]: string } = {
      'Aries': 'මේෂ',
      'Taurus': 'වෘෂභ',
      'Gemini': 'මිථුන',
      'Cancer': 'කටක',
      'Leo': 'සිංහ',
      'Virgo': 'කන්‍යා',
      'Libra': 'තුලා',
      'Scorpio': 'වෘශ්චික',
      'Sagittarius': 'ධනු',
      'Capricorn': 'මකර',
      'Aquarius': 'කුම්භ',
      'Pisces': 'මීන'
    };

    // Use direct Sinhala mapping if language is Sinhala
    if (language === 'si' && sinhalaSignMap[sign]) {
      console.log('🔤 Direct Sinhala translation:', {
        originalSign: sign,
        translatedSign: sinhalaSignMap[sign],
        language: language
      });
      return sinhalaSignMap[sign];
    }

    // Fallback to t() function for English or if direct mapping fails
    const signMap: { [key: string]: string } = {
      // Standard case
      'Aries': t('aries'),
      'Taurus': t('taurus'),
      'Gemini': t('gemini'),
      'Cancer': t('cancer'),
      'Leo': t('leo'),
      'Virgo': t('virgo'),
      'Libra': t('libra'),
      'Scorpio': t('scorpio'),
      'Sagittarius': t('sagittarius'),
      'Capricorn': t('capricorn'),
      'Aquarius': t('aquarius'),
      'Pisces': t('pisces'),
      // Lowercase variations
      'aries': t('aries'),
      'taurus': t('taurus'),
      'gemini': t('gemini'),
      'cancer': t('cancer'),
      'leo': t('leo'),
      'virgo': t('virgo'),
      'libra': t('libra'),
      'scorpio': t('scorpio'),
      'sagittarius': t('sagittarius'),
      'capricorn': t('capricorn'),
      'aquarius': t('aquarius'),
      'pisces': t('pisces'),
      // Uppercase variations
      'ARIES': t('aries'),
      'TAURUS': t('taurus'),
      'GEMINI': t('gemini'),
      'CANCER': t('cancer'),
      'LEO': t('leo'),
      'VIRGO': t('virgo'),
      'LIBRA': t('libra'),
      'SCORPIO': t('scorpio'),
      'SAGITTARIUS': t('sagittarius'),
      'CAPRICORN': t('capricorn'),
      'AQUARIUS': t('aquarius'),
      'PISCES': t('pisces')
    };

    // Debug logging for zodiac sign translation
    console.log('🔤 AstrologicalTables Zodiac Sign Translation:', {
      originalSign: sign,
      translatedSign: signMap[sign] || sign,
      availableTranslations: Object.keys(signMap),
      language: language
    });

    // Try exact match first
    if (signMap[sign]) {
      return signMap[sign];
    }

    // Try trimmed version
    const trimmedSign = sign.trim();
    if (signMap[trimmedSign]) {
      return signMap[trimmedSign];
    }

    // Try different case variations
    const variations = [
      sign.toLowerCase(),
      sign.toUpperCase(),
      sign.charAt(0).toUpperCase() + sign.slice(1).toLowerCase()
    ];

    for (const variation of variations) {
      if (signMap[variation]) {
        console.log('🔤 Found sign translation with variation:', { original: sign, variation, translated: signMap[variation] });
        return signMap[variation];
      }
    }

    // If no translation found, return original
    console.warn('🚨 No translation found for zodiac sign:', sign);
    return sign;
  };

  // Function to translate nakshatra names
  const translateNakshatra = (nakshatra: string): string => {
    if (!nakshatra) return nakshatra;

    // Direct translation mapping for Sinhala when language is 'si'
    const sinhalaNakshatraMap: { [key: string]: string } = {
      'Ashwini': 'අශ්විනී',
      'Bharani': 'භරණී',
      'Krittika': 'කෘත්තිකා',
      'Rohini': 'රෝහිණී',
      'Mrigashira': 'මෘගශිරා',
      'Ardra': 'ආර්ද්‍රා',
      'Punarvasu': 'පුනර්වසු',
      'Pushya': 'පුෂ්‍යා',
      'Ashlesha': 'ආශ්ලේෂා',
      'Magha': 'මඝා',
      'Purva Phalguni': 'පූර්ව ඵල්ගුනී',
      'Uttara Phalguni': 'උත්තර ඵල්ගුනී',
      'Hasta': 'හස්ත',
      'Chitra': 'චිත්‍රා',
      'Swati': 'ස්වාතී',
      'Vishakha': 'විශාඛා',
      'Anuradha': 'අනුරාධා',
      'Jyeshtha': 'ජ්‍යේෂ්ඨා',
      'Mula': 'මූලා',
      'Purva Ashadha': 'පූර්ව ආෂාඪා',
      'Uttara Ashadha': 'උත්තර ආෂාඪා',
      'Shravana': 'ශ්‍රවණ',
      'Dhanishta': 'ධනිෂ්ඨා',
      'Shatabhisha': 'ශතභිෂා',
      'Purva Bhadrapada': 'පූර්ව භද්‍රපදා',
      'Uttara Bhadrapada': 'උත්තර භද්‍රපදා',
      'Revati': 'රේවතී'
    };

    // Use direct Sinhala mapping if language is Sinhala
    if (language === 'si' && sinhalaNakshatraMap[nakshatra]) {
      console.log('🌟 Direct Sinhala nakshatra translation:', {
        originalNakshatra: nakshatra,
        translatedNakshatra: sinhalaNakshatraMap[nakshatra],
        language: language
      });
      return sinhalaNakshatraMap[nakshatra];
    }

    // Fallback to t() function for English or if direct mapping fails
    const nakshatraMap: { [key: string]: string } = {
      // Standard case
      'Ashwini': t('ashwini'),
      'Bharani': t('bharani'),
      'Krittika': t('krittika'),
      'Rohini': t('rohini'),
      'Mrigashira': t('mrigashira'),
      'Ardra': t('ardra'),
      'Punarvasu': t('punarvasu'),
      'Pushya': t('pushya'),
      'Ashlesha': t('ashlesha'),
      'Magha': t('magha'),
      'Purva Phalguni': t('purva_phalguni'),
      'Uttara Phalguni': t('uttara_phalguni'),
      'Hasta': t('hasta'),
      'Chitra': t('chitra'),
      'Swati': t('swati'),
      'Vishakha': t('vishakha'),
      'Anuradha': t('anuradha'),
      'Jyeshtha': t('jyeshtha'),
      'Mula': t('mula'),
      'Purva Ashadha': t('purva_ashadha'),
      'Uttara Ashadha': t('uttara_ashadha'),
      'Shravana': t('shravana'),
      'Dhanishta': t('dhanishta'),
      'Shatabhisha': t('shatabhisha'),
      'Purva Bhadrapada': t('purva_bhadrapada'),
      'Uttara Bhadrapada': t('uttara_bhadrapada'),
      'Revati': t('revati'),
      // Common spelling variations
      'Ashvini': t('ashwini'),
      'Kritika': t('krittika'),
      'Mrigasira': t('mrigashira'),
      'Arudra': t('ardra'),
      'Punarvasu': t('punarvasu'),
      'Pushyami': t('pushya'),
      'Ayilyam': t('ashlesha'),
      'Makam': t('magha'),
      'Pooram': t('purva_phalguni'),
      'Uthram': t('uttara_phalguni'),
      'Hastham': t('hasta'),
      'Chithira': t('chitra'),
      'Chothi': t('swati'),
      'Visakam': t('vishakha'),
      'Anusham': t('anuradha'),
      'Kettai': t('jyeshtha'),
      'Moola': t('mula'),
      'Pooradam': t('purva_ashadha'),
      'Uthradam': t('uttara_ashadha'),
      'Thiruvonam': t('shravana'),
      'Avittam': t('dhanishta'),
      'Chathayam': t('shatabhisha'),
      'Poorattathi': t('purva_bhadrapada'),
      'Uthrattathi': t('uttara_bhadrapada'),
      'Revathi': t('revati')
    };

    // Debug logging for nakshatra translation
    console.log('🌟 AstrologicalTables Nakshatra Translation:', {
      originalNakshatra: nakshatra,
      translatedNakshatra: nakshatraMap[nakshatra] || nakshatra,
      availableTranslations: Object.keys(nakshatraMap),
      language: language
    });

    // Try exact match first
    if (nakshatraMap[nakshatra]) {
      return nakshatraMap[nakshatra];
    }

    // Try trimmed version
    const trimmedNakshatra = nakshatra.trim();
    if (nakshatraMap[trimmedNakshatra]) {
      return nakshatraMap[trimmedNakshatra];
    }

    // Try different case variations
    const variations = [
      nakshatra.toLowerCase(),
      nakshatra.toUpperCase(),
      nakshatra.charAt(0).toUpperCase() + nakshatra.slice(1).toLowerCase()
    ];

    for (const variation of variations) {
      if (nakshatraMap[variation]) {
        console.log('🌟 Found nakshatra translation with variation:', { original: nakshatra, variation, translated: nakshatraMap[variation] });
        return nakshatraMap[variation];
      }
    }

    // If no translation found, return original
    console.warn('🚨 No translation found for nakshatra:', nakshatra);
    return nakshatra;
  };

  const renderKarakTable = () => {
    if (!karakTable) return null;
    
    return (
      <div className="bg-gradient-to-br from-yellow-900/30 to-orange-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-yellow-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent text-center">
          {t('karaka')}
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm md:text-base">
            <thead>
              <tr className="border-b-2 border-orange-500/40">
                <th className="text-left py-3 text-orange-200 font-bold">{t('karak')}</th>
                <th className="text-left py-3 text-orange-200 font-bold">{t('sthir')}</th>
                <th className="text-left py-3 text-orange-200 font-bold">{t('chara')}</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(karakTable).map(([karak, data]) => (
                <tr key={karak} className="border-b border-orange-500/20 hover:bg-orange-500/10 transition-colors">
                  <td className="py-3 text-white font-medium">{translateKaraka(karak)}</td>
                  <td className="py-3 text-yellow-100 font-medium">{translatePlanet(data.sthir)}</td>
                  <td className="py-3 text-yellow-100 font-medium">{translatePlanet(data.chara)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderAvasthaTable = () => {
    if (!avasthaTable) return null;

    return (
      <div className="bg-gradient-to-br from-blue-900/30 to-purple-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-blue-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent text-center">
          {t('avastha')}
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm md:text-base">
            <thead>
              <tr className="border-b-2 border-blue-500/40">
                <th className="text-left py-3 text-blue-200 font-bold">{t('planets')}</th>
                <th className="text-left py-3 text-blue-200 font-bold">{t('jagrat')}</th>
                <th className="text-left py-3 text-blue-200 font-bold">{t('baladi')}</th>
                <th className="text-left py-3 text-blue-200 font-bold">{t('deeptadi')}</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(avasthaTable).map(([planet, states]) => (
                <tr key={planet} className="border-b border-blue-500/20 hover:bg-blue-500/10 transition-colors">
                  <td className="py-3 text-white font-medium">{translatePlanet(planet)}</td>
                  <td className="py-3 text-blue-100 font-medium">{translateAvastha(states.jagrat)}</td>
                  <td className="py-3 text-blue-100 font-medium">{translateAvastha(states.baladi)}</td>
                  <td className="py-3 text-blue-100 font-medium">{translateAvastha(states.deeptadi)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderPlanetaryDetails = () => {
    if (!planetaryDetails) return null;

    // Debug: Test translation function
    console.log('🧪 Testing translation function:');
    console.log('  libra ->', t('libra'));
    console.log('  pisces ->', t('pisces'));
    console.log('  translateSign("Libra") ->', translateSign("Libra"));
    console.log('  translateSign("Pisces") ->', translateSign("Pisces"));
    
    return (
      <div className="bg-gradient-to-br from-green-900/30 to-teal-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-green-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-green-300 to-teal-300 bg-clip-text text-transparent text-center">
          {t('planetary_details')}
        </h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm md:text-base">
            <thead>
              <tr className="border-b-2 border-green-500/40">
                <th className="text-left py-3 text-green-200 font-bold">{t('planets')}</th>
                <th className="text-center py-3 text-green-200 font-bold">{t('c_r')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('rashi')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('longitude')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('nakshatra')}</th>
                <th className="text-center py-3 text-green-200 font-bold">{t('pada')}</th>
                <th className="text-left py-3 text-green-200 font-bold">{t('relation')}</th>
              </tr>
            </thead>
            <tbody>
              {planetaryDetails.map((detail) => (
                <tr key={detail.planet} className="border-b border-green-500/20 hover:bg-green-500/10 transition-colors">
                  <td className="py-3 text-white font-medium">{translatePlanet(detail.planet)}</td>
                  <td className="py-3 text-center">
                    <span className={detail.combust ? 'text-red-300 font-bold' : 'text-gray-400'}>
                      {detail.combust ? t('combust').charAt(0).toUpperCase() : '-'}
                    </span>
                    <span className="ml-2">
                      <span className={detail.retrograde ? 'text-red-300 font-bold' : 'text-gray-400'}>
                        {detail.retrograde ? t('retrograde_short') : '-'}
                      </span>
                    </span>
                  </td>
                  <td className="py-3 text-green-100 font-medium">
                    {(() => {
                      const translated = translateSign(detail.rashi);
                      console.log('🔍 Rashi translation:', {
                        original: detail.rashi,
                        translated: translated,
                        language: language,
                        directTranslation: t('libra')
                      });
                      return translated;
                    })()}
                  </td>
                  <td className="py-3 text-green-100 font-medium">{detail.longitude}</td>
                  <td className="py-3 text-green-100 font-medium">{translateNakshatra(detail.nakshatra)}</td>
                  <td className="py-3 text-center text-green-100 font-medium">{detail.pada}</td>
                  <td className="py-3">
                    <span className={getRelationColor(detail.relation)}>
                      {translateRelation(detail.relation)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  const renderVimshottariDasha = () => {
    if (!vimshottariDasha) return null;

    // Check if dasha data is valid
    const isValidDasha = vimshottariDasha.currentDasha !== 'Unknown' &&
                        vimshottariDasha.balance !== '0 years' &&
                        vimshottariDasha.periods && vimshottariDasha.periods.length > 0;

    if (!isValidDasha) {
      console.log('⚠️ Vimshottari Dasha data is invalid, hiding section');
      return null;
    }

    return (
      <div className="bg-gradient-to-br from-purple-900/30 to-pink-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-purple-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent text-center">
          {t('vimshottari_dasha')}
        </h3>

        <div className="mb-6 p-6 bg-purple-800/30 backdrop-blur-sm rounded-xl border border-purple-500/20">
          <p className="text-purple-100 text-lg">
            <span className="font-bold text-purple-200">{t('balance_of_dasha')}:</span> {translateBalance(vimshottariDasha.balance)}
          </p>
          <p className="text-purple-100 text-lg mt-2">
            <span className="font-bold text-purple-200">{t('current_dasha')}:</span> {translatePlanet(vimshottariDasha.currentDasha)}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-3">
          {vimshottariDasha.periods.slice(0, 9).map((period, index) => (
            <div key={index} className="flex justify-between items-center p-4 bg-purple-800/20 backdrop-blur-sm rounded-lg border border-purple-500/10 hover:bg-purple-500/20 transition-colors">
              <span className="text-white font-bold text-lg">{translatePlanet(period.planet)}</span>
              <span className="text-purple-100 font-medium">
                {new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderPanchang = () => {
    if (!panchang) return null;

    return (
      <div className="bg-gradient-to-br from-orange-900/30 to-red-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-orange-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-orange-300 to-red-300 bg-clip-text text-transparent text-center">
          {t('panchang')}
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-orange-800/30 backdrop-blur-sm rounded-xl p-4 border border-orange-500/20">
            <h4 className="text-orange-200 font-bold mb-2">{t('tithi')}</h4>
            <p className="text-white text-lg">{panchang.tithi}</p>
          </div>

          <div className="bg-orange-800/30 backdrop-blur-sm rounded-xl p-4 border border-orange-500/20">
            <h4 className="text-orange-200 font-bold mb-2">{t('nakshatra')}</h4>
            <p className="text-white text-lg">{translateNakshatra(panchang.nakshatra)}</p>
          </div>

          <div className="bg-orange-800/30 backdrop-blur-sm rounded-xl p-4 border border-orange-500/20">
            <h4 className="text-orange-200 font-bold mb-2">{t('yoga')}</h4>
            <p className="text-white text-lg">{panchang.yoga}</p>
          </div>

          <div className="bg-orange-800/30 backdrop-blur-sm rounded-xl p-4 border border-orange-500/20">
            <h4 className="text-orange-200 font-bold mb-2">{t('karana')}</h4>
            <p className="text-white text-lg">{panchang.karana}</p>
          </div>

          <div className="bg-orange-800/30 backdrop-blur-sm rounded-xl p-4 border border-orange-500/20">
            <h4 className="text-orange-200 font-bold mb-2">{t('vaar')}</h4>
            <p className="text-white text-lg">{panchang.vaar}</p>
          </div>
        </div>
      </div>
    );
  };

  const renderDoshaAnalysis = () => {
    if (!doshaAnalysis) return null;

    return (
      <div className="bg-gradient-to-br from-red-900/30 to-pink-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-red-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-red-300 to-pink-300 bg-clip-text text-transparent text-center">
          {t('dosha_analysis')}
        </h3>

        <div className="space-y-4">
          {/* Mangal Dosha */}
          <div className="bg-red-800/30 backdrop-blur-sm rounded-xl p-4 border border-red-500/20">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-red-200 font-bold">{t('mangal_dosha')}</h4>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                doshaAnalysis.mangalDosha.present
                  ? 'bg-red-500/20 text-red-200'
                  : 'bg-green-500/20 text-green-200'
              }`}>
                {doshaAnalysis.mangalDosha.present ? t('present') : t('absent')}
              </span>
            </div>
            <p className="text-white text-sm">{doshaAnalysis.mangalDosha.description}</p>
            {doshaAnalysis.mangalDosha.present && (
              <p className="text-red-300 text-sm mt-2">
                {t('severity')}: {doshaAnalysis.mangalDosha.severity}
              </p>
            )}
          </div>

          {/* Kaal Sarp Dosha */}
          <div className="bg-red-800/30 backdrop-blur-sm rounded-xl p-4 border border-red-500/20">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-red-200 font-bold">{t('kaal_sarp_dosha')}</h4>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                doshaAnalysis.kaalSarpDosha.present
                  ? 'bg-red-500/20 text-red-200'
                  : 'bg-green-500/20 text-green-200'
              }`}>
                {doshaAnalysis.kaalSarpDosha.present ? t('present') : t('absent')}
              </span>
            </div>
            <p className="text-white text-sm">{doshaAnalysis.kaalSarpDosha.description}</p>
          </div>
        </div>
      </div>
    );
  };

  const renderYogaAnalysis = () => {
    if (!yogaAnalysis) return null;

    const hasYogas = yogaAnalysis.rajaYogas.length > 0 ||
                    yogaAnalysis.dhanaYogas.length > 0 ||
                    yogaAnalysis.otherYogas.length > 0;

    if (!hasYogas) return null;

    return (
      <div className="bg-gradient-to-br from-green-900/30 to-emerald-900/30 backdrop-blur-sm rounded-2xl p-6 md:p-8 border border-green-500/20 shadow-2xl">
        <h3 className="text-xl md:text-2xl font-bold text-white mb-6 md:mb-8 bg-gradient-to-r from-green-300 to-emerald-300 bg-clip-text text-transparent text-center">
          {t('yoga_analysis')}
        </h3>

        <div className="space-y-6">
          {/* Raja Yogas */}
          {yogaAnalysis.rajaYogas.length > 0 && (
            <div className="bg-green-800/30 backdrop-blur-sm rounded-xl p-4 border border-green-500/20">
              <h4 className="text-green-200 font-bold mb-3">{t('raja_yogas')}</h4>
              <ul className="space-y-2">
                {yogaAnalysis.rajaYogas.map((yoga, index) => (
                  <li key={index} className="text-white text-sm flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    {yoga}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Dhana Yogas */}
          {yogaAnalysis.dhanaYogas.length > 0 && (
            <div className="bg-green-800/30 backdrop-blur-sm rounded-xl p-4 border border-green-500/20">
              <h4 className="text-green-200 font-bold mb-3">{t('dhana_yogas')}</h4>
              <ul className="space-y-2">
                {yogaAnalysis.dhanaYogas.map((yoga, index) => (
                  <li key={index} className="text-white text-sm flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    {yoga}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAshtakavarga = () => {
    if (!ashtakavarga) return null;
    
    return (
      <div className="bg-gradient-to-br from-indigo-900/20 to-blue-900/20 rounded-xl p-6">
        <h3 className="text-xl font-bold text-white mb-4">{t('prastharashtakvarga')}</h3>

        {/* Sun Table */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-yellow-300 mb-2">{t('sun').toUpperCase()}</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-xs border border-yellow-500/30">
              <thead>
                <tr className="bg-yellow-900/20">
                  <th className="border border-yellow-500/30 p-1 text-yellow-300"></th>
                  {['Ar', 'Ta', 'Ge', 'Ca', 'Le', 'Vi', 'Li', 'Sc', 'Sa', 'Cp', 'Aq', 'Pi', 'Total'].map(sign => (
                    <th key={sign} className="border border-yellow-500/30 p-1 text-yellow-300">{translateSignAbbr(sign)}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {['SUN', 'MOON', 'MARS', 'MERCURY', 'JUPITER', 'VENUS', 'SATURN', 'Ascendant'].map((planet, planetIndex) => (
                  <tr key={planet}>
                    <td className="border border-yellow-500/30 p-1 text-white font-medium">{translatePlanet(planet)}</td>
                    {ashtakavarga.sunTable[planetIndex]?.map((score, houseIndex) => (
                      <td key={houseIndex} className="border border-yellow-500/30 p-1 text-center text-yellow-200">
                        {score}
                      </td>
                    )) || Array(12).fill(0).map((_, i) => (
                      <td key={i} className="border border-yellow-500/30 p-1 text-center text-yellow-200">0</td>
                    ))}
                    <td className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                      {ashtakavarga.sunTable[planetIndex]?.reduce((sum, score) => sum + score, 0) || 0}
                    </td>
                  </tr>
                ))}
                <tr className="bg-yellow-900/20">
                  <td className="border border-yellow-500/30 p-1 text-yellow-300 font-bold">{t('total')}</td>
                  {Array(12).fill(0).map((_, houseIndex) => {
                    const total = ashtakavarga.sunTable.reduce((sum, row) => sum + (row[houseIndex] || 0), 0);
                    return (
                      <td key={houseIndex} className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                        {total}
                      </td>
                    );
                  })}
                  <td className="border border-yellow-500/30 p-1 text-center text-yellow-300 font-bold">
                    {ashtakavarga.sarvashtakavarga.reduce((sum, score) => sum + score, 0)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Sarvashtakavarga Summary */}
        <div className="mt-4 p-4 bg-indigo-800/20 rounded-lg">
          <h4 className="text-lg font-semibold text-indigo-300 mb-2">{t('sarvashtakavarga_summary')}</h4>
          <div className="grid grid-cols-6 gap-2 text-sm">
            {ashtakavarga.sarvashtakavarga.map((score, index) => (
              <div key={index} className="text-center">
                <div className="text-indigo-300">{t('house')} {index + 1}</div>
                <div className="text-white font-bold">{score}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const getRelationColor = (relation: string): string => {
    switch (relation) {
      case 'Own': return 'text-green-400';
      case 'Friendly': return 'text-blue-400';
      case 'Enemy': return 'text-red-400';
      case 'Neutral': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="space-y-6">
      {renderPanchang()}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {renderKarakTable()}
        {renderAvasthaTable()}
      </div>

      {renderPlanetaryDetails()}
      {renderVimshottariDasha()}
      {renderDoshaAnalysis()}
      {renderYogaAnalysis()}
      {renderAshtakavarga()}
    </div>
  );
}
