// UI Text Translations
// This file contains all static UI text translations for the application

import { useCallback } from 'react';
import { useLanguage } from '@/hooks/useLanguage';

export type UITextKey =
  // Navigation and Tabs
  | 'navigation'
  | 'horoscope'
  | 'daily_guide'
  | 'today_cosmic_guide'
  | 'general_reading'
  | 'love_relationships'
  | 'career_money'
  | 'health_wellness'
  | 'todays_lucky_elements'
  | 'lucky_number'
  | 'lucky_color'
  | 'lucky_time'
  | 'lucky_gem'
  | 'daily_advice'
  | 'compatible_signs_today'
  | 'welcome'
  | 'loading_cosmic_insights'
  | 'error_loading_dashboard'
  | 'configuration_error'
  | 'session_expire_warning'
  | 'settings'
  | 'filter'
  | 'add_user'
  | 'logout'
  | 'confirm_logout'
  | 'confirm_logout_title'
  | 'cancel'
  | 'admin_user'
  | 'manage_users_content_analytics'
  | 'overview'
  | 'users'
  | 'content'
  | 'personal_horoscopes'
  | 'analytics'
  | 'search_users'
  | 'name'
  | 'email'
  | 'phone'
  | 'zodiac'
  | 'language'
  | 'scans'
  | 'last_active'
  | 'actions'
  | 'never'
  | 'showing_users'
  | 'previous'
  | 'next'
  | 'daily_guides'
  | 'horoscopes'
  | 'all_signs'
  | 'all_types'
  | 'all_languages'
  | 'english'
  | 'sinhala'
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'intense'
  | 'research_investigation'
  | 'pay_attention_body'
  | 'trust_intuition'
  | 'deep_insights'
  | 'intense_emotional_connections'
  | 'dig_deeper_projects'
  | 'detoxification_cleansing'
  // Dashboard Messages
  | 'no_personal_horoscope_available'
  | 'personalized_horoscope_content_message'
  // Landing Page
  | 'your_personal_horoscope_daily_guide'
  | 'discover_cosmic_destiny'
  | 'daily_horoscopes'
  | 'daily_horoscopes_description'
  | 'lucky_guidance'
  | 'lucky_guidance_description'
  | 'qr_access'
  | 'qr_access_description'
  | 'scan_qr_code'
  | 'upload_qr_image'
  | 'scan_your_qr_code'
  | 'point_camera_qr_description'
  | 'qr_card_description'
  | 'qr_compatibility_tip'
  // QR Scanner
  | 'back'
  | 'scan_error'
  | 'scan_success'
  | 'redirecting'
  | 'processing'
  | 'scan_instructions'
  | 'position_qr_code'
  | 'ensure_good_lighting'
  | 'hold_steady'
  | 'qr_code_hint'
  | 'invalid_qr_code'
  | 'qr_upload_hint'
  | 'camera_access_hint'
  | 'scanner_error'
  | 'processing_image'
  | 'analyzing_uploaded_image'
  | 'upload_image_instead'
  | 'use_camera'
  | 'choose_file'
  | 'loading_scanner_library'
  | 'starting_camera'
  | 'try_scanning_again'
  | 'dismiss'
  | 'back_to_home'
  | 'camera_scanner'
  | 'camera_permission_denied'
  | 'no_camera_found'
  | 'camera_not_supported'
  | 'qr_scanner_not_initialized'
  | 'failed_to_read_file'
  | 'try_upload_instead'
  // Error Messages
  | 'something_went_wrong'
  | 'try_again'
  | 'admin_dashboard_error'
  | 'no_daily_guide_available'
  | 'daily_guide_check_back'
  // Admin Components
  | 'astroconnect_admin'
  | 'total_users'
  | 'active_users'
  | 'total_horoscopes'
  | 'qr_scans'
  | 'active_today'
  | 'total_scans'
  | 'daily_guides_generated'
  | 'recent_activity'
  | 'no_recent_activity'
  | 'admin_login'
  | 'access_admin_dashboard'
  | 'username'
  | 'password'
  | 'login'
  | 'invalid_credentials'
  | 'email_address'
  | 'enter_your_email'
  | 'enter_your_password'
  | 'signing_in'
  | 'sign_in'
  | 'demo_credentials'
  | 'secure_admin_access'
  | 'login_failed'
  | 'network_error_try_again'
  // Loading States
  | 'loading_ellipsis'
  // System Settings
  | 'default_language'
  | 'system_settings'
  | 'default_language_description'
  | 'save_changes'
  | 'changes_saved_successfully'
  | 'failed_to_save_changes'
  | 'loading'
  | 'saving'

  // Horoscope/Birth Chart UI
  | 'birth_chart'
  | 'handahana'
  | 'lagna_chart'
  | 'navamsa_chart'
  | 'chandra_chart'
  | 'charts'
  | 'tables'
  | 'astrological_data_tables'
  | 'enhanced_vedic_charts'
  | 'karaka'
  | 'avastha'
  | 'planetary_details'
  | 'vimshottari_dasha'
  | 'ashtakavarga'
  | 'panchang'
  | 'tithi'
  | 'yoga'
  | 'karana'
  | 'vaar'
  | 'dosha_analysis'
  | 'mangal_dosha'
  | 'kaal_sarp_dosha'
  | 'present'
  | 'absent'
  | 'severity'
  | 'yoga_analysis'
  | 'raja_yogas'
  | 'dhana_yogas'
  | 'other_yogas'
  | 'planets'
  | 'jagrat'
  | 'baladi'
  | 'deeptadi'
  | 'c_r'
  | 'rashi'
  | 'longitude'
  | 'nakshatra'
  | 'pada'
  | 'relation'
  | 'houses_numbered_clockwise'
  | 'planet_abbreviations'
  | 'retrograde_motion'
  | 'sun'
  | 'moon'
  | 'mars'
  | 'mercury'
  | 'jupiter'
  | 'venus'
  | 'saturn'
  | 'ascendant'
  | 'birth_details'
  | 'astrological_signs'
  | 'chart_status'
  | 'generated'
  | 'your_birth_chart_awaits'
  | 'cosmic_blueprint_description'
  | 'birth_time_place_required'
  | 'calculate_my_birth_chart'
  | 'contact_admin_birth_details'
  | 'enhanced_vedic_charts_not_available'
  | 'basic_system_generated'
  | 'enhanced_charts_missing'
  | 'astrological_tables_not_available'
  | 'enhanced_tables_missing'
  | 'readings'
  | 'general_reading'
  | 'strengths_weaknesses'
  | 'career_guidance'
  | 'relationship_guidance'
  | 'health_guidance'
  | 'birth_chart_readings'
  | 'readings_not_available'
  | 'readings_generated_automatically'
  | 'personalized_vedic_charts_description'
  | 'houses_numbered_clockwise'
  | 'planet_abbreviations'
  | 'retrograde_motion'
  | 'chart_data_not_available'
  | 'chart_being_calculated'
  | 'refresh_page_moment'

  // Zodiac Signs
  | 'aries'
  | 'taurus'
  | 'gemini'
  | 'cancer'
  | 'leo'
  | 'virgo'
  | 'libra'
  | 'scorpio'
  | 'sagittarius'
  | 'capricorn'
  | 'aquarius'
  | 'pisces'

  // Additional Astrological Terms
  | 'rahu'
  | 'ketu'
  | 'uranus'
  | 'neptune'
  | 'pluto'
  | 'chiron'
  | 'sirius'
  | 'sign'
  | 'house'

  // Nakshatras (27 Lunar Mansions)
  | 'ashwini'
  | 'bharani'
  | 'krittika'
  | 'rohini'
  | 'mrigashira'
  | 'ardra'
  | 'punarvasu'
  | 'pushya'
  | 'ashlesha'
  | 'magha'
  | 'purva_phalguni'
  | 'uttara_phalguni'
  | 'hasta'
  | 'chitra'
  | 'swati'
  | 'vishakha'
  | 'anuradha'
  | 'jyeshtha'
  | 'mula'
  | 'purva_ashadha'
  | 'uttara_ashadha'
  | 'shravana'
  | 'dhanishta'
  | 'shatabhisha'
  | 'purva_bhadrapada'
  | 'uttara_bhadrapada'
  | 'revati'

  // Planet symbols/abbreviations for charts
  | 'sun_symbol'
  | 'moon_symbol'
  | 'mars_symbol'
  | 'mercury_symbol'
  | 'jupiter_symbol'
  | 'venus_symbol'
  | 'saturn_symbol'
  | 'rahu_symbol'
  | 'ketu_symbol'
  | 'uranus_symbol'
  | 'neptune_symbol'
  | 'pluto_symbol'
  | 'chiron_symbol'
  | 'sirius_symbol'
  | 'degree'
  | 'minute'
  | 'second'
  | 'personalized_vedic_charts_description'
  | 'balance_of_dasha'
  | 'current_dasha'

  // Missing UI translations for horoscope page
  | 'loading_birth_chart'
  | 'calculating_cosmic_blueprint'
  | 'error_loading_birth_chart'
  | 'try_again'
  | 'debug_info'
  | 'available'
  | 'missing'
  | 'lagna_chart_available'
  | 'navamsa_chart_available'
  | 'chandra_chart_available'
  | 'planet_positions_available'
  | 'karaka_table_available'
  | 'avastha_table_available'
  | 'planetary_details_available'
  | 'vimshottari_dasha_available'
  | 'ashtakavarga_available'
  | 'error_loading_charts'
  | 'error_loading_charts_message'
  | 'error_loading_tables'
  | 'error_loading_tables_message'
  | 'error_loading_readings'
  | 'error_loading_readings_message'
  | 'rising_sign'
  | 'moon_sign'
  | 'sun_sign'
  | 'planetary_positions'
  | 'retrograde'
  | 'calculated_using_vedic'
  | 'strengths_weaknesses'
  | 'career_guidance'
  | 'relationship_guidance'
  | 'health_guidance'
  | 'enhanced_vedic_charts_not_available'
  | 'enhanced_tables_missing'
  | 'chart_data_not_available'
  | 'chart_being_calculated'
  | 'houses_numbered_1_12'
  | 'planet_symbols_used'
  | 'r_indicates_retrograde'

  // Astrological table headers
  | 'karak'
  | 'sthir'
  | 'chara'
  | 'combust'
  | 'retrograde_short'
  // Ashtakavarga specific translations
  | 'prastharashtakvarga'
  | 'sarvashtakavarga_summary'
  | 'total'
  // Zodiac sign abbreviations
  | 'ar'
  | 'ta'
  | 'ge'
  | 'ca'
  | 'le'
  | 'vi'
  | 'li'
  | 'sc'
  | 'sa'
  | 'cp'
  | 'aq'
  | 'pi'
  // Planetary relation status
  | 'own'
  | 'friendly'
  | 'enemy'
  | 'neutral'
  // Additional astrological terms
  | 'years'
  | 'months'
  | 'days'
  // Karaka (Significator) translations
  | 'alma'
  | 'dara'
  | 'gnati'
  | 'matru'
  | 'putra'
  | 'amatya'
  | 'bhratru'
  // Avastha (Planetary States) translations
  | 'swapna'
  | 'jaagrat'
  | 'yuva'
  | 'deena'
  | 'kumar'
  | 'bala'
  | 'swatha'
  | 'mrat'
  | 'muditha'
  | 'susupla'
  | 'vadha'
  | 'deepta'
  | 'khal'
  | 'shant';

export const UI_TRANSLATIONS: Record<UITextKey, { en: string; si: string }> = {
  // Navigation and Tabs
  navigation: {
    en: 'Navigation',
    si: 'සංචාලනය'
  },
  horoscope: {
    en: 'Horoscope',
    si: 'ජ්‍යොතිෂය'
  },
  daily_guide: {
    en: 'Daily Guide',
    si: 'දෛනික මාර්ගෝපදේශය'
  },
  today_cosmic_guide: {
    en: "Today's Cosmic Guide",
    si: 'අද දිනේ කොස්මික් මාර්ගෝපදේශය'
  },
  general_reading: {
    en: 'General Reading',
    si: 'සාමාන්‍ය කියවීම'
  },
  love_relationships: {
    en: 'Love & Relationships',
    si: 'ආදරය සහ සබඳතා'
  },
  career_money: {
    en: 'Career & Money',
    si: 'වෘත්තිය සහ මුදල්'
  },
  health_wellness: {
    en: 'Health & Wellness',
    si: 'සෞඛ්‍යය සහ යහපැවැත්ම'
  },
  todays_lucky_elements: {
    en: "Today's Lucky Elements",
    si: 'අද දිනේ වාසනාවන්ත අංග'
  },
  lucky_number: {
    en: 'Lucky Number',
    si: 'වාසනාවන්ත අංකය'
  },
  lucky_color: {
    en: 'Lucky Color',
    si: 'වාසනාවන්ත වර්ණය'
  },
  lucky_time: {
    en: 'Lucky Time',
    si: 'වාසනාවන්ත වේලාව'
  },
  lucky_gem: {
    en: 'Lucky Gem',
    si: 'වාසනාවන්ත මැණික්'
  },
  daily_advice: {
    en: 'Daily Advice',
    si: 'දෛනික උපදෙස්'
  },
  compatible_signs_today: {
    en: 'Compatible Signs Today',
    si: 'අද දිනේ ගැළපෙන රාශි'
  },
  welcome: {
    en: 'Welcome',
    si: 'ආයුබෝවන්'
  },
  loading_cosmic_insights: {
    en: 'Loading your cosmic insights...',
    si: 'ඔබේ කොස්මික් අවබෝධය පූරණය වෙමින්...'
  },
  error_loading_dashboard: {
    en: 'Error Loading Dashboard',
    si: 'ඩෑෂ්බෝඩ් පූරණයේ දෝෂයක්'
  },
  configuration_error: {
    en: 'Configuration Error',
    si: 'වින්‍යාස දෝෂයක්'
  },
  session_expire_warning: {
    en: 'Your session will expire in',
    si: 'ඔබේ සැසිය අවසන් වනු ඇත්තේ'
  },
  settings: {
    en: 'Settings',
    si: 'සැකසුම්'
  },
  filter: {
    en: 'Filter',
    si: 'පෙරහන'
  },
  add_user: {
    en: 'Add User',
    si: 'පරිශීලකයා එක් කරන්න'
  },
  logout: {
    en: 'Logout',
    si: 'ඉවත් වන්න'
  },
  confirm_logout: {
    en: 'Are you sure you want to logout?',
    si: 'ඔබට ඉවත් වීමට අවශ්‍ය බව විශ්වාසද?'
  },
  confirm_logout_title: {
    en: 'Confirm Logout',
    si: 'ඉවත්වීම තහවුරු කරන්න'
  },
  cancel: {
    en: 'Cancel',
    si: 'අවලංගු කරන්න'
  },
  admin_user: {
    en: 'Admin User',
    si: 'පරිපාලක පරිශීලකයා'
  },
  manage_users_content_analytics: {
    en: 'Manage users, content, and analytics',
    si: 'පරිශීලකයින්, අන්තර්ගතය සහ විශ්ලේෂණ කළමනාකරණය කරන්න'
  },
  overview: {
    en: 'Overview',
    si: 'දළ විශ්ලේෂණය'
  },
  users: {
    en: 'Users',
    si: 'පරිශීලකයින්'
  },
  content: {
    en: 'Content',
    si: 'අන්තර්ගතය'
  },
  personal_horoscopes: {
    en: 'Personal Horoscopes',
    si: 'පුද්ගලික ජ්‍යොතිෂය'
  },
  analytics: {
    en: 'Analytics',
    si: 'විශ්ලේෂණ'
  },
  search_users: {
    en: 'Search users...',
    si: 'පරිශීලකයින් සොයන්න...'
  },
  name: {
    en: 'Name',
    si: 'නම'
  },
  email: {
    en: 'Email',
    si: 'ඊමේල්'
  },
  phone: {
    en: 'Phone',
    si: 'දුරකථනය'
  },
  zodiac: {
    en: 'Zodiac',
    si: 'රාශිය'
  },
  language: {
    en: 'Language',
    si: 'භාෂාව'
  },
  scans: {
    en: 'Scans',
    si: 'ස්කෑන්'
  },
  last_active: {
    en: 'Last Active',
    si: 'අවසන් ක්‍රියාකාරිත්වය'
  },
  actions: {
    en: 'Actions',
    si: 'ක්‍රියාමාර්ග'
  },
  never: {
    en: 'Never',
    si: 'කිසි විටෙකත්'
  },
  showing_users: {
    en: 'Showing',
    si: 'පෙන්වමින්'
  },
  previous: {
    en: 'Previous',
    si: 'පෙර'
  },
  next: {
    en: 'Next',
    si: 'ඊළඟ'
  },
  daily_guides: {
    en: 'Daily Guides',
    si: 'දෛනික මාර්ගෝපදේශ'
  },
  horoscopes: {
    en: 'Horoscopes',
    si: 'ජ්‍යොතිෂ'
  },
  all_signs: {
    en: 'All Signs',
    si: 'සියලුම රාශි'
  },
  all_types: {
    en: 'All Types',
    si: 'සියලුම වර්ග'
  },
  all_languages: {
    en: 'All Languages',
    si: 'සියලුම භාෂා'
  },
  english: {
    en: 'English',
    si: 'ඉංග්‍රීසි'
  },
  sinhala: {
    en: 'Sinhala',
    si: 'සිංහල'
  },
  daily: {
    en: 'Daily',
    si: 'දෛනික'
  },
  weekly: {
    en: 'Weekly',
    si: 'සතිපතා'
  },
  monthly: {
    en: 'Monthly',
    si: 'මාසික'
  },
  intense: {
    en: 'Intense',
    si: 'තීව්‍ර'
  },
  research_investigation: {
    en: 'Research and investigation skills are highlighted. Dig deeper into projects to uncover hidden opportunities.',
    si: 'පර්යේෂණ සහ විමර්ශන කුසලතා ඉස්මතු වේ. සැඟවුණු අවස්ථා සොයා ගැනීම සඳහා ව්‍යාපෘති ගැඹුරින් විමර්ශනය කරන්න.'
  },
  pay_attention_body: {
    en: 'Pay attention to your body\'s signals. Detoxification and cleansing activities are particularly beneficial today.',
    si: 'ඔබේ ශරීරයේ සංඥා වලට අවධානය යොමු කරන්න. විෂ ඉවත් කිරීම සහ පිරිසිදු කිරීමේ ක්‍රියාකාරකම් අද විශේෂයෙන් ප්‍රයෝජනවත් වේ.'
  },
  trust_intuition: {
    en: 'Trust your intuition in making important decisions. Your inner wisdom is particularly strong today.',
    si: 'වැදගත් තීරණ ගැනීමේදී ඔබේ අභ්‍යන්තර ප්‍රඥාව විශ්වාස කරන්න. ඔබේ අභ්‍යන්තර ප්‍රඥාව අද විශේෂයෙන් ශක්තිමත් වේ.'
  },
  deep_insights: {
    en: 'Deep insights and intuitive understanding guide you today. Trust your inner wisdom in all decisions.',
    si: 'ගැඹුරු අවබෝධය සහ අභ්‍යන්තර අවබෝධය අද ඔබට මග පෙන්වයි. සියලු තීරණවලදී ඔබේ අභ්‍යන්තර ප්‍රඥාව විශ්වාස කරන්න.'
  },
  intense_emotional_connections: {
    en: 'Intense emotional connections are possible today. Existing relationships deepen, and new ones may have profound significance.',
    si: 'අද තීව්‍ර චිත්තවේගීය සම්බන්ධතා ඇති විය හැක. පවතින සබඳතා ගැඹුරු වන අතර නව ඒවා ගැඹුරු වැදගත්කමක් ගත හැක.'
  },
  dig_deeper_projects: {
    en: 'Research and investigation skills are highlighted. Dig deeper into projects to uncover hidden opportunities.',
    si: 'පර්යේෂණ සහ විමර්ශන කුසලතා ඉස්මතු වේ. සැඟවුණු අවස්ථා සොයා ගැනීම සඳහා ව්‍යාපෘති ගැඹුරින් විමර්ශනය කරන්න.'
  },
  detoxification_cleansing: {
    en: 'Pay attention to your body\'s signals. Detoxification and cleansing activities are particularly beneficial today.',
    si: 'ඔබේ ශරීරයේ සංඥා වලට අවධානය යොමු කරන්න. විෂ ඉවත් කිරීම සහ පිරිසිදු කිරීමේ ක්‍රියාකාරකම් අද විශේෂයෙන් ප්‍රයෝජනවත් වේ.'
  },

  // Dashboard Messages
  no_personal_horoscope_available: {
    en: 'No Personal Horoscope Available',
    si: 'පුද්ගලික ජ්‍යොතිෂයක් නොමැත'
  },
  personalized_horoscope_content_message: {
    en: 'Your personalized horoscope content will appear here once added by the admin.',
    si: 'පරිපාලකයා විසින් එකතු කළ පසු ඔබේ පුද්ගලික ජ්‍යොතිෂ අන්තර්ගතය මෙහි දිස්වනු ඇත.'
  },

  // Landing Page
  your_personal_horoscope_daily_guide: {
    en: 'Your Personal Horoscope & Daily Guide',
    si: 'ඔබේ පුද්ගලික ජ්‍යොතිෂය සහ දෛනික මාර්ගෝපදේශය'
  },
  discover_cosmic_destiny: {
    en: 'Discover your cosmic destiny with personalized astrology insights',
    si: 'පුද්ගලික ජ්‍යොතිෂ අවබෝධයන් සමඟ ඔබේ කොස්මික් ඉරණම සොයා ගන්න'
  },
  daily_horoscopes: {
    en: 'Daily Horoscopes',
    si: 'දෛනික ජ්‍යොතිෂය'
  },
  daily_horoscopes_description: {
    en: 'Get personalized daily, weekly, and monthly predictions based on your zodiac sign',
    si: 'ඔබේ රාශි ලකුණ මත පදනම්ව පුද්ගලික දෛනික, සතිපතා සහ මාසික පුරෝකථන ලබා ගන්න'
  },
  lucky_guidance: {
    en: 'Lucky Guidance',
    si: 'වාසනාවන්ත මාර්ගෝපදේශය'
  },
  lucky_guidance_description: {
    en: 'Discover your lucky numbers, colors, and optimal times for important decisions',
    si: 'වැදගත් තීරණ සඳහා ඔබේ වාසනාවන්ත අංක, වර්ණ සහ ප්‍රශස්ත කාලයන් සොයා ගන්න'
  },
  qr_access: {
    en: 'QR Access',
    si: 'QR ප්‍රවේශය'
  },
  qr_access_description: {
    en: 'Instant access to your personalized dashboard with your unique QR code',
    si: 'ඔබේ අද්විතීය QR කේතය සමඟ ඔබේ පුද්ගලික ඩෑෂ්බෝඩ් වෙත ක්ෂණික ප්‍රවේශය'
  },
  scan_qr_code: {
    en: 'Scan QR Code',
    si: 'QR කේතය ස්කෑන් කරන්න'
  },
  upload_qr_image: {
    en: 'Upload QR Image',
    si: 'QR රූපය උඩුගත කරන්න'
  },
  scan_your_qr_code: {
    en: 'Scan Your QR Code',
    si: 'ඔබේ QR කේතය ස්කෑන් කරන්න'
  },
  point_camera_qr_description: {
    en: 'Point your camera at the QR code or upload an image containing the QR code',
    si: 'ඔබේ කැමරාව QR කේතය වෙත යොමු කරන්න හෝ QR කේතය අඩංගු රූපයක් උඩුගත කරන්න'
  },
  qr_card_description: {
    en: 'Have a personalized QR card? Scan it to access your cosmic insights instantly',
    si: 'පුද්ගලික QR කාඩ්පතක් තිබේද? ඔබේ කොස්මික් අවබෝධයන් ක්ෂණිකව ප්‍රවේශ කිරීමට එය ස්කෑන් කරන්න'
  },
  qr_compatibility_tip: {
    en: '💡 Tip: Only AstroConnect QR codes will work here. Other QR codes (websites, WiFi, etc.) are not compatible.',
    si: '💡 ඉඟිය: මෙහි AstroConnect QR කේත පමණක් ක්‍රියා කරයි. අනෙකුත් QR කේත (වෙබ් අඩවි, WiFi, ආදිය) ගැළපෙන්නේ නැත.'
  },

  // QR Scanner
  back: {
    en: 'Back',
    si: 'ආපසු'
  },
  scan_error: {
    en: 'Scan Error',
    si: 'ස්කෑන් දෝෂයක්'
  },
  scan_success: {
    en: 'Scan Successful',
    si: 'ස්කෑන් කිරීම සාර්ථකයි'
  },
  redirecting: {
    en: 'Redirecting...',
    si: 'හරවා යවමින්...'
  },
  processing: {
    en: 'Processing...',
    si: 'සැකසෙමින්...'
  },
  scan_instructions: {
    en: 'Scan Instructions',
    si: 'ස්කෑන් උපදෙස්'
  },
  position_qr_code: {
    en: 'Position the QR code within the frame',
    si: 'QR කේතය රාමුව තුළ ස්ථානගත කරන්න'
  },
  ensure_good_lighting: {
    en: 'Ensure good lighting for better results',
    si: 'වඩා හොඳ ප්‍රතිඵල සඳහා හොඳ ආලෝකයක් සහතික කරන්න'
  },
  hold_steady: {
    en: 'Hold your device steady',
    si: 'ඔබේ උපාංගය ස්ථිරව අල්ලා ගන්න'
  },
  qr_code_hint: {
    en: 'For best results, use your device\'s back camera',
    si: 'වඩා හොඳ ප්‍රතිඵල සඳහා ඔබේ උපාංගයේ පිටුපස කැමරාව භාවිතා කරන්න'
  },
  invalid_qr_code: {
    en: 'Invalid QR code. Please scan a valid AstroConnect QR code.',
    si: 'වලංගු නොවන QR කේතයක්. කරුණාකර වලංගු AstroConnect QR කේතයක් ස්කෑන් කරන්න.'
  },
  qr_upload_hint: {
    en: 'Or upload a clear image containing the QR code',
    si: 'හෝ QR කේතය අඩංගු පැහැදිලි රූපයක් උඩුගත කරන්න'
  },
  camera_access_hint: {
    en: 'Camera access will be requested when you click "Use Camera"',
    si: '"කැමරාව භාවිතා කරන්න" ක්ලික් කරන විට කැමරා ප්‍රවේශය ඉල්ලනු ලැබේ'
  },
  scanner_error: {
    en: 'Scanner Error',
    si: 'ස්කෑනර් දෝෂයක්'
  },
  processing_image: {
    en: 'Processing Image...',
    si: 'රූපය සැකසෙමින්...'
  },
  analyzing_uploaded_image: {
    en: 'Analyzing uploaded image...',
    si: 'උඩුගත කළ රූපය විශ්ලේෂණය කරමින්...'
  },
  upload_image_instead: {
    en: 'Or upload an image instead',
    si: 'හෝ ඒ වෙනුවට රූපයක් උඩුගත කරන්න'
  },
  use_camera: {
    en: 'Use Camera',
    si: 'කැමරාව භාවිතා කරන්න'
  },
  choose_file: {
    en: 'Choose File',
    si: 'ගොනුව තෝරන්න'
  },
  loading_scanner_library: {
    en: 'Loading scanner library...',
    si: 'ස්කෑනර් පුස්තකාලය පූරණය වෙමින්...'
  },
  starting_camera: {
    en: 'Starting camera...',
    si: 'කැමරාව ආරම්භ කරමින්...'
  },
  try_scanning_again: {
    en: 'Try Scanning Again',
    si: 'නැවත ස්කෑන් කරන්න'
  },
  dismiss: {
    en: 'Dismiss',
    si: 'ඉවත් කරන්න'
  },
  back_to_home: {
    en: 'Back to Home',
    si: 'මුල් පිටුවට'
  },
  camera_scanner: {
    en: 'Camera Scanner',
    si: 'කැමරා ස්කෑනරය'
  },
  camera_permission_denied: {
    en: 'Camera permission denied. Please allow camera access and try again.',
    si: 'කැමරා අවසරය ප්‍රතික්ෂේප කරන ලදී. කරුණාකර කැමරා ප්‍රවේශයට අවසර දී නැවත උත්සාහ කරන්න.'
  },
  no_camera_found: {
    en: 'No camera found on this device.',
    si: 'මෙම උපාංගයේ කැමරාවක් හමු නොවීය.'
  },
  camera_not_supported: {
    en: 'Camera not supported on this device.',
    si: 'මෙම උපාංගයේ කැමරාව සහාය නොදක්වයි.'
  },
  qr_scanner_not_initialized: {
    en: 'QR code scanner not properly initialized. Please refresh the page and try again.',
    si: 'QR කේත ස්කෑනරය නිසි ලෙස ආරම්භ කර නැත. කරුණාකර පිටුව නැවුම් කර නැවත උත්සාහ කරන්න.'
  },
  failed_to_read_file: {
    en: 'Failed to read the uploaded file. Please try again.',
    si: 'උඩුගත කළ ගොනුව කියවීමට අසමත් විය. කරුණාකර නැවත උත්සාහ කරන්න.'
  },
  try_upload_instead: {
    en: '💡 Try using "Upload Image" instead if camera access is blocked',
    si: '💡 කැමරා ප්‍රවේශය අවහිර කර ඇත්නම් ඒ වෙනුවට "රූපය උඩුගත කරන්න" භාවිතා කර බලන්න'
  },

  // Error Messages
  something_went_wrong: {
    en: 'Something went wrong',
    si: 'යමක් වැරදී ගියේය'
  },
  try_again: {
    en: 'Try Again',
    si: 'නැවත උත්සාහ කරන්න'
  },
  admin_dashboard_error: {
    en: 'Admin Dashboard Error',
    si: 'පරිපාලක ඩෑෂ්බෝඩ් දෝෂයක්'
  },
  no_daily_guide_available: {
    en: 'No Daily Guide Available',
    si: 'දෛනික මාර්ගෝපදේශයක් නොමැත'
  },
  daily_guide_check_back: {
    en: 'Your daily cosmic guide will appear here automatically. Please check back later.',
    si: 'ඔබේ දෛනික කොස්මික් මාර්ගෝපදේශය මෙහි ස්වයංක්‍රීයව දිස් වනු ඇත. කරුණාකර පසුව නැවත පරීක්ෂා කරන්න.'
  },

  // Admin Components
  astroconnect_admin: {
    en: 'AstroConnect Admin',
    si: 'AstroConnect පරිපාලක'
  },
  total_users: {
    en: 'Total Users',
    si: 'සම්පූර්ණ පරිශීලකයින්'
  },
  active_users: {
    en: 'Active Users',
    si: 'ක්‍රියාකාරී පරිශීලකයින්'
  },
  total_horoscopes: {
    en: 'Birth Charts Generated',
    si: 'උත්පාදිත හදහන'
  },
  qr_scans: {
    en: 'QR Scans',
    si: 'QR ස්කෑන්'
  },
  active_today: {
    en: 'Active Today',
    si: 'අද ක්‍රියාකාරී'
  },
  total_scans: {
    en: 'Total Scans',
    si: 'සම්පූර්ණ ස්කෑන්'
  },
  daily_guides_generated: {
    en: 'Daily Guides Generated',
    si: 'ජනනය කළ දෛනික මාර්ගෝපදේශ'
  },
  recent_activity: {
    en: 'Recent Activity',
    si: 'මෑත ක්‍රියාකාරකම්'
  },
  no_recent_activity: {
    en: 'No recent activity',
    si: 'මෑත ක්‍රියාකාරකම් නොමැත'
  },
  admin_login: {
    en: 'Admin Login',
    si: 'පරිපාලක පිවිසුම'
  },
  access_admin_dashboard: {
    en: 'Access the admin dashboard',
    si: 'පරිපාලක ඩෑෂ්බෝඩ් වෙත ප්‍රවේශ වන්න'
  },
  username: {
    en: 'Username',
    si: 'පරිශීලක නාමය'
  },
  password: {
    en: 'Password',
    si: 'මුරපදය'
  },
  login: {
    en: 'Login',
    si: 'පිවිසෙන්න'
  },
  invalid_credentials: {
    en: 'Invalid credentials',
    si: 'වලංගු නොවන අක්තපත්‍ර'
  },
  email_address: {
    en: 'Email Address',
    si: 'ඊමේල් ලිපිනය'
  },
  enter_your_email: {
    en: 'Enter your email',
    si: 'ඔබේ ඊමේල් ඇතුළත් කරන්න'
  },
  enter_your_password: {
    en: 'Enter your password',
    si: 'ඔබේ මුරපදය ඇතුළත් කරන්න'
  },
  signing_in: {
    en: 'Signing In...',
    si: 'පිවිසෙමින්...'
  },
  sign_in: {
    en: 'Sign In',
    si: 'පිවිසෙන්න'
  },
  demo_credentials: {
    en: 'Demo Credentials:',
    si: 'ආදර්ශන අක්තපත්‍ර:'
  },
  secure_admin_access: {
    en: 'Secure admin access to AstroConnect',
    si: 'AstroConnect වෙත ආරක්ෂිත පරිපාලක ප්‍රවේශය'
  },
  login_failed: {
    en: 'Login failed',
    si: 'පිවිසීම අසාර්ථක විය'
  },
  network_error_try_again: {
    en: 'Network error. Please try again.',
    si: 'ජාල දෝෂයක්. කරුණාකර නැවත උත්සාහ කරන්න.'
  },

  // Loading States
  loading_ellipsis: {
    en: 'Loading...',
    si: 'පූරණය වෙමින්...'
  },

  // System Settings
  default_language: {
    en: 'Default Language',
    si: 'පෙරනිමි භාෂාව'
  },
  system_settings: {
    en: 'System Settings',
    si: 'පද්ධති සැකසුම්'
  },
  default_language_description: {
    en: 'Set the default language for new users',
    si: 'නව පරිශීලකයින් සඳහා පෙරනිමි භාෂාව සකසන්න'
  },
  save_changes: {
    en: 'Save Changes',
    si: 'වෙනස්කම් සුරකින්න'
  },
  changes_saved_successfully: {
    en: 'Changes saved successfully',
    si: 'වෙනස්කම් සාර්ථකව සුරකින ලදී'
  },
  failed_to_save_changes: {
    en: 'Failed to save changes',
    si: 'වෙනස්කම් සුරැකීමට අසමත් විය'
  },
  loading: {
    en: 'Loading',
    si: 'පූරණය වෙමින්'
  },
  saving: {
    en: 'Saving',
    si: 'සුරකිමින්'
  },

  // Horoscope/Birth Chart UI
  birth_chart: {
    en: 'Birth Chart',
    si: 'ජන්ම කුණ්ඩලිය'
  },
  handahana: {
    en: 'Handahana',
    si: 'හඳහන'
  },
  lagna_chart: {
    en: 'Lagna Chart',
    si: 'ලග්න කුණ්ඩලිය'
  },
  navamsa_chart: {
    en: 'Navamsa Chart',
    si: 'නවම්ශ කුණ්ඩලිය'
  },
  chandra_chart: {
    en: 'Chandra Chart',
    si: 'චන්ද්‍ර කුණ්ඩලිය'
  },
  charts: {
    en: 'Charts',
    si: 'කුණ්ඩලි'
  },
  tables: {
    en: 'Tables',
    si: 'වගු'
  },
  astrological_data_tables: {
    en: 'Astrological Data Tables',
    si: 'ජ්‍යොතිෂ දත්ත වගු'
  },
  enhanced_vedic_charts: {
    en: 'Enhanced Vedic Charts',
    si: 'වැඩිදියුණු කළ වෛදික කුණ්ඩලි'
  },
  karaka: {
    en: 'Karaka',
    si: 'කාරක'
  },
  avastha: {
    en: 'Avastha',
    si: 'අවස්ථා'
  },
  planetary_details: {
    en: 'Planetary Details',
    si: 'ග්‍රහ විස්තර'
  },
  vimshottari_dasha: {
    en: 'Vimshottari Dasha',
    si: 'විම්ශෝත්තරී දශා'
  },
  ashtakavarga: {
    en: 'Ashtakavarga',
    si: 'අෂ්ටකවර්ග'
  },
  panchang: {
    en: 'Panchang',
    si: 'පංචාංග'
  },
  tithi: {
    en: 'Tithi',
    si: 'තිථි'
  },
  yoga: {
    en: 'Yoga',
    si: 'යෝග'
  },
  karana: {
    en: 'Karana',
    si: 'කරණ'
  },
  vaar: {
    en: 'Day',
    si: 'දිනය'
  },
  dosha_analysis: {
    en: 'Dosha Analysis',
    si: 'දෝෂ විශ්ලේෂණය'
  },
  mangal_dosha: {
    en: 'Mangal Dosha',
    si: 'මංගල දෝෂය'
  },
  kaal_sarp_dosha: {
    en: 'Kaal Sarp Dosha',
    si: 'කාල සර්ප දෝෂය'
  },
  present: {
    en: 'Present',
    si: 'පවතී'
  },
  absent: {
    en: 'Absent',
    si: 'නැත'
  },
  severity: {
    en: 'Severity',
    si: 'තීව්‍රතාව'
  },
  yoga_analysis: {
    en: 'Yoga Analysis',
    si: 'යෝග විශ්ලේෂණය'
  },
  raja_yogas: {
    en: 'Raja Yogas',
    si: 'රාජ යෝග'
  },
  dhana_yogas: {
    en: 'Dhana Yogas',
    si: 'ධන යෝග'
  },
  other_yogas: {
    en: 'Other Yogas',
    si: 'අනෙකුත් යෝග'
  },
  planets: {
    en: 'Planets',
    si: 'ග්‍රහයන්'
  },
  jagrat: {
    en: 'Jagrat',
    si: 'ජාග්‍රත්'
  },
  baladi: {
    en: 'Baladi',
    si: 'බාලාදි'
  },
  deeptadi: {
    en: 'Deeptadi',
    si: 'දීප්තාදි'
  },
  c_r: {
    en: 'C R',
    si: 'ද ප'
  },
  rashi: {
    en: 'Rashi',
    si: 'රාශිය'
  },
  longitude: {
    en: 'Longitude',
    si: 'දේශාංශය'
  },
  nakshatra: {
    en: 'Nakshatra',
    si: 'නක්ෂත්‍රය'
  },
  pada: {
    en: 'Pada',
    si: 'පාදය'
  },
  relation: {
    en: 'Relation',
    si: 'සම්බන්ධතාවය'
  },
  houses_numbered_clockwise: {
    en: 'Houses are numbered 1-12 clockwise from top',
    si: 'ගෘහ ඉහළින් දක්ෂිණාවර්තව 1-12 ලෙස අංකනය කර ඇත'
  },
  planet_abbreviations: {
    en: 'Planets: Su=Sun, Mo=Moon, Ma=Mars, Me=Mercury, Ju=Jupiter, Ve=Venus, Sa=Saturn',
    si: 'ග්‍රහයන්: Su=සූර්ය, Mo=චන්ද්‍ර, Ma=අඟහරු, Me=බුධ, Ju=ගුරු, Ve=ශුක්‍ර, Sa=සෙනසුරු'
  },
  retrograde_motion: {
    en: 'ᴿ indicates retrograde motion',
    si: 'ᴿ වක්‍ර ගමනය දක්වයි'
  },
  sun: {
    en: 'Sun',
    si: 'සූර්ය'
  },
  moon: {
    en: 'Moon',
    si: 'චන්ද්‍ර'
  },
  mars: {
    en: 'Mars',
    si: 'අඟහරු'
  },
  mercury: {
    en: 'Mercury',
    si: 'බුධ'
  },
  jupiter: {
    en: 'Jupiter',
    si: 'ගුරු'
  },
  venus: {
    en: 'Venus',
    si: 'ශුක්‍ර'
  },
  saturn: {
    en: 'Saturn',
    si: 'සෙනසුරු'
  },
  ascendant: {
    en: 'Ascendant',
    si: 'ලග්නය'
  },
  birth_details: {
    en: 'Birth Details',
    si: 'ජන්ම විස්තර'
  },
  astrological_signs: {
    en: 'Astrological Signs',
    si: 'ජ්‍යොතිෂ ලකුණු'
  },
  chart_status: {
    en: 'Chart Status',
    si: 'කුණ්ඩලි තත්ත්වය'
  },
  generated: {
    en: 'Generated',
    si: 'ජනනය කරන ලදී'
  },
  your_birth_chart_awaits: {
    en: 'Your Birth Chart (Handahana) Awaits',
    si: 'ඔබේ ජන්ම කුණ්ඩලිය (හඳහන) බලාපොරොත්තුවෙන්'
  },
  cosmic_blueprint_description: {
    en: 'Discover your cosmic blueprint with a personalized Vedic astrology birth chart. Get detailed interpretations of your planetary positions, houses, and life guidance based on your exact birth time and location.',
    si: 'පුද්ගලික වෛදික ජ්‍යොතිෂ ජන්ම කුණ්ඩලියක් සමඟ ඔබේ කොස්මික් සැලැස්ම සොයා ගන්න. ඔබේ නිශ්චිත ජන්ම වේලාව සහ ස්ථානය මත පදනම්ව ඔබේ ග්‍රහ ස්ථාන, ගෘහ සහ ජීවන මාර්ගෝපදේශ පිළිබඳ සවිස්තරාත්මක අර්ථකථන ලබා ගන්න.'
  },
  birth_time_place_required: {
    en: 'Birth time and place are required for accurate calculations.',
    si: 'නිවැරදි ගණනය කිරීම් සඳහා ජන්ම වේලාව සහ ස්ථානය අවශ්‍ය වේ.'
  },
  calculate_my_birth_chart: {
    en: 'Calculate My Birth Chart',
    si: 'මගේ ජන්ම කුණ්ඩලිය ගණනය කරන්න'
  },
  contact_admin_birth_details: {
    en: 'Please contact admin to update your birth details.',
    si: 'ඔබේ ජන්ම විස්තර යාවත්කාලීන කිරීමට කරුණාකර පරිපාලකයා සම්බන්ධ කර ගන්න.'
  },
  enhanced_vedic_charts_not_available: {
    en: 'Enhanced Vedic chart calculations are not available for this birth chart.',
    si: 'මෙම ජන්ම කුණ්ඩලිය සඳහා වැඩිදියුණු කළ වෛදික කුණ්ඩලි ගණනය කිරීම් නොමැත.'
  },
  basic_system_generated: {
    en: 'This birth chart was generated with the basic system. The enhanced Vedic charts (Lagna, Navamsa, Chandra) are not available.',
    si: 'මෙම ජන්ම කුණ්ඩලිය මූලික පද්ධතිය සමඟ ජනනය කරන ලදී. වැඩිදියුණු කළ වෛදික කුණ්ඩලි (ලග්න, නවම්ශ, චන්ද්‍ර) නොමැත.'
  },
  enhanced_charts_missing: {
    en: 'The enhanced Vedic charts (Lagna, Navamsa, Chandra) are not available.',
    si: 'වැඩිදියුණු කළ වෛදික කුණ්ඩලි (ලග්න, නවම්ශ, චන්ද්‍ර) නොමැත.'
  },
  astrological_tables_not_available: {
    en: 'Enhanced astrological data tables are not available for this birth chart.',
    si: 'මෙම ජන්ම කුණ්ඩලිය සඳහා වැඩිදියුණු කළ ජ්‍යොතිෂ දත්ත වගු නොමැත.'
  },
  enhanced_tables_missing: {
    en: 'This birth chart was generated with the basic system. The enhanced astrological tables (Karaka, Avastha, Dasha) are not available.',
    si: 'මෙම ජන්ම කුණ්ඩලිය මූලික පද්ධතිය සමඟ ජනනය කරන ලදී. වැඩිදියුණු කළ ජ්‍යොතිෂ වගු (කාරක, අවස්ථා, දශා) නොමැත.'
  },
  readings: {
    en: 'Readings',
    si: 'කියවීම්'
  },
  strengths_weaknesses: {
    en: 'Strengths & Weaknesses',
    si: 'ශක්තීන් සහ දුර්වලතා'
  },
  career_guidance: {
    en: 'Career Guidance',
    si: 'වෘත්තීය මාර්ගෝපදේශය'
  },
  relationship_guidance: {
    en: 'Relationship Guidance',
    si: 'සබඳතා මාර්ගෝපදේශය'
  },
  health_guidance: {
    en: 'Health Guidance',
    si: 'සෞඛ්‍ය මාර්ගෝපදේශය'
  },
  birth_chart_readings: {
    en: 'Birth Chart Readings',
    si: 'ජන්ම කුණ්ඩලි කියවීම්'
  },
  readings_not_available: {
    en: 'Detailed readings and interpretations are not available for this birth chart.',
    si: 'මෙම ජන්ම කුණ්ඩලිය සඳහා සවිස්තරාත්මක කියවීම් සහ අර්ථකථන නොමැත.'
  },
  readings_generated_automatically: {
    en: 'Readings will be generated automatically when the birth chart is created with the enhanced system.',
    si: 'වැඩිදියුණු කළ පද්ධතිය සමඟ ජන්ම කුණ්ඩලිය නිර්මාණය කරන විට කියවීම් ස්වයංක්‍රීයව ජනනය වේ.'
  },
  personalized_vedic_charts_description: {
    en: 'Your personalized Vedic astrology charts with detailed planetary positions and cosmic insights',
    si: 'සවිස්තරාත්මක ග්‍රහ ස්ථාන සහ කොස්මික් අවබෝධයන් සහිත ඔබේ පුද්ගලික වෛදික ජ්‍යොතිෂ කුණ්ඩලි'
  },
  chart_data_not_available: {
    en: 'Chart Data Not Available',
    si: 'කුණ්ඩලි දත්ත නොමැත'
  },
  chart_being_calculated: {
    en: 'Chart data is being calculated. Please refresh the page in a moment.',
    si: 'කුණ්ඩලි දත්ත ගණනය කරමින් පවතී. කරුණාකර මොහොතකින් පිටුව නැවුම් කරන්න.'
  },
  refresh_page_moment: {
    en: 'Please refresh the page in a moment',
    si: 'කරුණාකර මොහොතකින් පිටුව නැවුම් කරන්න'
  },

  // Zodiac Signs
  aries: {
    en: 'Aries',
    si: 'මේෂ'
  },
  taurus: {
    en: 'Taurus',
    si: 'වෘෂභ'
  },
  gemini: {
    en: 'Gemini',
    si: 'මිථුන'
  },
  cancer: {
    en: 'Cancer',
    si: 'කටක'
  },
  leo: {
    en: 'Leo',
    si: 'සිංහ'
  },
  virgo: {
    en: 'Virgo',
    si: 'කන්‍යා'
  },
  libra: {
    en: 'Libra',
    si: 'තුලා'
  },
  scorpio: {
    en: 'Scorpio',
    si: 'වෘශ්චික'
  },
  sagittarius: {
    en: 'Sagittarius',
    si: 'ධනු'
  },
  capricorn: {
    en: 'Capricorn',
    si: 'මකර'
  },
  aquarius: {
    en: 'Aquarius',
    si: 'කුම්භ'
  },
  pisces: {
    en: 'Pisces',
    si: 'මීන'
  },

  // Additional Astrological Terms
  rahu: {
    en: 'Rahu',
    si: 'රාහු'
  },
  ketu: {
    en: 'Ketu',
    si: 'කේතු'
  },
  uranus: {
    en: 'Uranus',
    si: 'යුරේනස්'
  },
  neptune: {
    en: 'Neptune',
    si: 'නෙප්චූන්'
  },
  pluto: {
    en: 'Pluto',
    si: 'ප්ලූටෝ'
  },
  chiron: {
    en: 'Chiron',
    si: 'චිරෝන්'
  },
  sirius: {
    en: 'Sirius',
    si: 'සිරියස්'
  },
  sign: {
    en: 'Sign',
    si: 'ලකුණ'
  },
  house: {
    en: 'House',
    si: 'ගෘහය'
  },
  degree: {
    en: 'Degree',
    si: 'අංශක'
  },
  minute: {
    en: 'Minute',
    si: 'මිනිත්තු'
  },
  second: {
    en: 'Second',
    si: 'තත්පර'
  },
  balance_of_dasha: {
    en: 'Balance of Dasha',
    si: 'දශා ශේෂය'
  },
  current_dasha: {
    en: 'Current Dasha',
    si: 'වර්තමාන දශාව'
  },

  // Missing UI translations for horoscope page
  loading_birth_chart: {
    en: 'Loading Birth Chart',
    si: 'ජන්ම කුණ්ඩලිය පූරණය වෙමින්'
  },
  calculating_cosmic_blueprint: {
    en: 'Calculating your cosmic blueprint...',
    si: 'ඔබේ කොස්මික් සැලැස්ම ගණනය කරමින්...'
  },
  error_loading_birth_chart: {
    en: 'Error Loading Birth Chart',
    si: 'ජන්ම කුණ්ඩලිය පූරණයේ දෝෂයක්'
  },
  debug_info: {
    en: 'Debug Info',
    si: 'දෝෂ නිරාකරණ තොරතුරු'
  },
  available: {
    en: 'Available',
    si: 'ලබා ගත හැකි'
  },
  missing: {
    en: 'Missing',
    si: 'අස්ථානගත'
  },
  lagna_chart_available: {
    en: 'Lagna Chart',
    si: 'ලග්න කුණ්ඩලිය'
  },
  navamsa_chart_available: {
    en: 'Navamsa Chart',
    si: 'නවම්ශ කුණ්ඩලිය'
  },
  chandra_chart_available: {
    en: 'Chandra Chart',
    si: 'චන්ද්‍ර කුණ්ඩලිය'
  },
  planet_positions_available: {
    en: 'Planet Positions',
    si: 'ග්‍රහ ස්ථාන'
  },
  karaka_table_available: {
    en: 'Karaka Table',
    si: 'කාරක වගුව'
  },
  avastha_table_available: {
    en: 'Avastha Table',
    si: 'අවස්ථා වගුව'
  },
  planetary_details_available: {
    en: 'Planetary Details',
    si: 'ග්‍රහ විස්තර'
  },
  vimshottari_dasha_available: {
    en: 'Vimshottari Dasha',
    si: 'විම්ශෝත්තරී දශාව'
  },
  ashtakavarga_available: {
    en: 'Ashtakavarga',
    si: 'අෂ්ටකවර්ග'
  },
  error_loading_charts: {
    en: 'Error Loading Charts',
    si: 'කුණ්ඩලි පූරණයේ දෝෂයක්'
  },
  error_loading_charts_message: {
    en: 'There was an error loading the Vedic charts. Please try refreshing the page.',
    si: 'වෛදික කුණ්ඩලි පූරණය කිරීමේදී දෝෂයක් ඇතිවිය. කරුණාකර පිටුව නැවුම් කරන්න.'
  },
  error_loading_tables: {
    en: 'Error Loading Tables',
    si: 'වගු පූරණයේ දෝෂයක්'
  },
  error_loading_tables_message: {
    en: 'There was an error loading the astrological tables. Please try refreshing the page.',
    si: 'ජ්‍යොතිෂ වගු පූරණය කිරීමේදී දෝෂයක් ඇතිවිය. කරුණාකර පිටුව නැවුම් කරන්න.'
  },
  error_loading_readings: {
    en: 'Error Loading Readings',
    si: 'කියවීම් පූරණයේ දෝෂයක්'
  },
  error_loading_readings_message: {
    en: 'There was an error loading the birth chart readings. Please try refreshing the page.',
    si: 'ජන්ම කුණ්ඩලි කියවීම් පූරණය කිරීමේදී දෝෂයක් ඇතිවිය. කරුණාකර පිටුව නැවුම් කරන්න.'
  },
  rising_sign: {
    en: 'Rising Sign',
    si: 'ලග්න ලකුණ'
  },
  moon_sign: {
    en: 'Moon Sign',
    si: 'චන්ද්‍ර ලකුණ'
  },
  sun_sign: {
    en: 'Sun Sign',
    si: 'සූර්ය ලකුණ'
  },
  planetary_positions: {
    en: 'Planetary Positions',
    si: 'ග්‍රහ ස්ථාන'
  },
  retrograde: {
    en: 'Retrograde',
    si: 'ප්‍රතිගාමී'
  },
  calculated_using_vedic: {
    en: 'Calculated using Vedic Astrology principles',
    si: 'වෛදික ජ්‍යොතිෂ මූලධර්ම භාවිතයෙන් ගණනය කරන ලදී'
  },
  houses_numbered_1_12: {
    en: 'Houses numbered 1-12',
    si: 'ගෘහ 1-12 අංකනය කර ඇත'
  },
  planet_symbols_used: {
    en: 'Planet symbols used',
    si: 'ග්‍රහ සංකේත භාවිතා කර ඇත'
  },
  r_indicates_retrograde: {
    en: 'R indicates retrograde',
    si: 'R ප්‍රතිගාමී දක්වයි'
  },

  // Astrological table headers
  karak: {
    en: 'Karak',
    si: 'කාරක'
  },
  sthir: {
    en: 'Sthir',
    si: 'ස්ථිර'
  },
  chara: {
    en: 'Chara',
    si: 'චර'
  },
  combust: {
    en: 'Combust',
    si: 'දග්ධ'
  },
  retrograde_short: {
    en: 'R',
    si: 'ප්‍ර'
  },

  // Ashtakavarga specific translations
  prastharashtakvarga: {
    en: 'Prastharashtakvarga',
    si: 'ප්‍රස්ථාරාෂ්ටකවර්ග'
  },
  sarvashtakavarga_summary: {
    en: 'Sarvashtakavarga Summary',
    si: 'සර්වාෂ්ටකවර්ග සාරාංශය'
  },
  total: {
    en: 'Total',
    si: 'එකතුව'
  },

  // Zodiac sign abbreviations for Ashtakavarga tables
  ar: {
    en: 'Ar',
    si: 'මේ'
  },
  ta: {
    en: 'Ta',
    si: 'වෘ'
  },
  ge: {
    en: 'Ge',
    si: 'මි'
  },
  ca: {
    en: 'Ca',
    si: 'ක'
  },
  le: {
    en: 'Le',
    si: 'සි'
  },
  vi: {
    en: 'Vi',
    si: 'කන්'
  },
  li: {
    en: 'Li',
    si: 'තු'
  },
  sc: {
    en: 'Sc',
    si: 'වෘශ්'
  },
  sa: {
    en: 'Sa',
    si: 'ධ'
  },
  cp: {
    en: 'Cp',
    si: 'මක'
  },
  aq: {
    en: 'Aq',
    si: 'කුම්'
  },
  pi: {
    en: 'Pi',
    si: 'මී'
  },

  // Planetary relation status
  own: {
    en: 'Own',
    si: 'ස්වකීය'
  },
  friendly: {
    en: 'Friendly',
    si: 'මිත්‍ර'
  },
  enemy: {
    en: 'Enemy',
    si: 'සතුරු'
  },
  neutral: {
    en: 'Neutral',
    si: 'මධ්‍යස්ථ'
  },

  // Additional astrological terms that might appear in charts
  years: {
    en: 'years',
    si: 'වර්ෂ'
  },
  months: {
    en: 'months',
    si: 'මාස'
  },
  days: {
    en: 'days',
    si: 'දින'
  },

  // Karaka (Significator) translations
  alma: {
    en: 'Alma',
    si: 'ආත්මකාරක'
  },
  dara: {
    en: 'Dara',
    si: 'දාරකාරක'
  },
  gnati: {
    en: 'Gnati',
    si: 'ගණතිකාරක'
  },
  matru: {
    en: 'Matru',
    si: 'මාතෘකාරක'
  },
  putra: {
    en: 'Putra',
    si: 'පුත්‍රකාරක'
  },
  amatya: {
    en: 'Amatya',
    si: 'අමාත්‍යකාරක'
  },
  bhratru: {
    en: 'Bhratru',
    si: 'භ්‍රාතෘකාරක'
  },

  // Avastha (Planetary States) translations
  swapna: {
    en: 'Swapna',
    si: 'ස්වප්න'
  },
  jaagrat: {
    en: 'Jaagrat',
    si: 'ජාග්‍රත්'
  },
  yuva: {
    en: 'Yuva',
    si: 'යුව'
  },
  deena: {
    en: 'Deena',
    si: 'දීන'
  },
  kumar: {
    en: 'Kumar',
    si: 'කුමාර'
  },
  bala: {
    en: 'Bala',
    si: 'බාල'
  },
  swatha: {
    en: 'Swatha',
    si: 'ස්වස්ථ'
  },
  mrat: {
    en: 'Mrat',
    si: 'මෘත'
  },
  muditha: {
    en: 'Muditha',
    si: 'මුදිත'
  },
  susupla: {
    en: 'Susupla',
    si: 'සුසුප්ත'
  },
  vadha: {
    en: 'Vadha',
    si: 'වධ'
  },
  deepta: {
    en: 'Deepta',
    si: 'දීප්ත'
  },
  khal: {
    en: 'Khal',
    si: 'ඛල'
  },
  shant: {
    en: 'Shant',
    si: 'ශාන්ත'
  },

  // Planet symbols/abbreviations for charts
  sun_symbol: {
    en: 'Su',
    si: 'සූ'
  },
  moon_symbol: {
    en: 'Mo',
    si: 'චන්'
  },
  mars_symbol: {
    en: 'Ma',
    si: 'අඟ'
  },
  mercury_symbol: {
    en: 'Me',
    si: 'බු'
  },
  jupiter_symbol: {
    en: 'Ju',
    si: 'ගු'
  },
  venus_symbol: {
    en: 'Ve',
    si: 'ශු'
  },
  saturn_symbol: {
    en: 'Sa',
    si: 'සෙ'
  },
  rahu_symbol: {
    en: 'Ra',
    si: 'රා'
  },
  ketu_symbol: {
    en: 'Ke',
    si: 'කේ'
  },
  uranus_symbol: {
    en: 'Ur',
    si: 'යු'
  },
  neptune_symbol: {
    en: 'Ne',
    si: 'නෙ'
  },
  pluto_symbol: {
    en: 'Pl',
    si: 'ප්ලූ'
  },
  chiron_symbol: {
    en: 'Ch',
    si: 'චි'
  },
  sirius_symbol: {
    en: 'Si',
    si: 'සි'
  },

  // Nakshatras (27 Lunar Mansions)
  ashwini: {
    en: 'Ashwini',
    si: 'අශ්විනී'
  },
  bharani: {
    en: 'Bharani',
    si: 'භරණී'
  },
  krittika: {
    en: 'Krittika',
    si: 'කෘත්තිකා'
  },
  rohini: {
    en: 'Rohini',
    si: 'රෝහිණී'
  },
  mrigashira: {
    en: 'Mrigashira',
    si: 'මෘගශිරා'
  },
  ardra: {
    en: 'Ardra',
    si: 'ආර්ද්‍රා'
  },
  punarvasu: {
    en: 'Punarvasu',
    si: 'පුනර්වසු'
  },
  pushya: {
    en: 'Pushya',
    si: 'පුෂ්‍යා'
  },
  ashlesha: {
    en: 'Ashlesha',
    si: 'ආශ්ලේෂා'
  },
  magha: {
    en: 'Magha',
    si: 'මඝා'
  },
  purva_phalguni: {
    en: 'Purva Phalguni',
    si: 'පූර්ව ඵල්ගුනී'
  },
  uttara_phalguni: {
    en: 'Uttara Phalguni',
    si: 'උත්තර ඵල්ගුනී'
  },
  hasta: {
    en: 'Hasta',
    si: 'හස්ත'
  },
  chitra: {
    en: 'Chitra',
    si: 'චිත්‍රා'
  },
  swati: {
    en: 'Swati',
    si: 'ස්වාතී'
  },
  vishakha: {
    en: 'Vishakha',
    si: 'විශාඛා'
  },
  anuradha: {
    en: 'Anuradha',
    si: 'අනුරාධා'
  },
  jyeshtha: {
    en: 'Jyeshtha',
    si: 'ජ්‍යේෂ්ඨා'
  },
  mula: {
    en: 'Mula',
    si: 'මූලා'
  },
  purva_ashadha: {
    en: 'Purva Ashadha',
    si: 'පූර්ව ආෂාඪා'
  },
  uttara_ashadha: {
    en: 'Uttara Ashadha',
    si: 'උත්තර ආෂාඪා'
  },
  shravana: {
    en: 'Shravana',
    si: 'ශ්‍රවණ'
  },
  dhanishta: {
    en: 'Dhanishta',
    si: 'ධනිෂ්ඨා'
  },
  shatabhisha: {
    en: 'Shatabhisha',
    si: 'ශතභිෂා'
  },
  purva_bhadrapada: {
    en: 'Purva Bhadrapada',
    si: 'පූර්ව භද්‍රපදා'
  },
  uttara_bhadrapada: {
    en: 'Uttara Bhadrapada',
    si: 'උත්තර භද්‍රපදා'
  },
  revati: {
    en: 'Revati',
    si: 'රේවතී'
  }
};

/**
 * Get UI text translation for a given key and language
 */
export function getUIText(key: UITextKey, language: 'en' | 'si' = 'en'): string {
  const translation = UI_TRANSLATIONS[key];
  if (!translation) {
    console.warn(`UI translation not found for key: ${key}`);
    return key; // Return the key as fallback
  }
  return translation[language] || translation.en; // Fallback to English if translation missing
}

/**
 * Hook for using UI translations with current language context
 */
export function useUITranslation() {
  const { language } = useLanguage();

  // Use useCallback to ensure the function is recreated when language changes
  const t = useCallback((key: UITextKey): string => {
    const translation = getUIText(key, language);
    // Debug logging for key translations
    if (key === 'today_cosmic_guide' || key === 'general_reading' || key === 'daily_guide') {
      console.log('🔤 UI Translation:', { key, language, translation });
    }
    return translation;
  }, [language]); // Dependency on language ensures recreation when language changes

  return { t };
}
