# AstroConnect - Personalized Horoscope & Daily Guide Web Application

AstroConnect is a comprehensive astrology web application that provides personalized horoscope readings, daily guidance, and birth chart analysis through QR code authentication. Built with Next.js 15, TypeScript, and Prisma with PostgreSQL, it offers a seamless bilingual experience for users seeking cosmic insights.

## ✨ Key Features

### 🔐 Authentication & Security
- **QR Code Authentication**: Secure, passwordless login using unique QR codes printed on personalized cards
- **Dual QR Scanning**: Camera scanning AND file upload options for maximum accessibility
- **Token-based Security**: Cryptographically secure UUID tokens with permanent user mapping
- **Rate Limiting**: API protection against brute force attacks
- **HTTPS Enforcement**: All traffic encrypted with comprehensive security headers

### 🌟 Core Astrology Features
- **Daily Horoscopes**: AI-generated personalized daily, weekly, and monthly predictions
- **Birth Chart Analysis**: Complete Vedic astrology charts (Lagna chart, Navamsa chart) with detailed calculations
- **Daily Guides**: Automated daily guidance with lucky numbers, colors, times, and personalized suggestions
- **Zodiac Integration**: Complete zodiac sign system with proper astrological calculations
- **Real-time Updates**: Dynamic content loading with intelligent caching

### 🌍 Multi-language Support
- **Bilingual Interface**: Complete English and Sinhala language support
- **AI-powered Translation**: Google Gemini API integration for natural translations
- **Real-time Language Switching**: Instant translation without page reload or logout
- **Pre-translated Content**: Daily guides stored separately in both languages
- **Proper Sinhala Script**: Planet names and astrological terms in authentic Sinhala letters

### 📱 User Experience
- **Progressive Web App**: Installable with offline capabilities and mobile optimization
- **Responsive Design**: Mobile-first approach optimized for all screen sizes
- **Modern UI**: Clean, intuitive interface with smooth animations and purple theme
- **Custom Dialogs**: Beautiful confirmation dialogs with backdrop blur effects
- **Minimalist Mobile Menu**: Professional mobile navigation with elegant animations

### 👨‍💼 Admin Panel
- **Dual Admin Roles**: Super Admin (can remove all admins) and Admin (cannot remove admins)
- **User Management**: Complete user lifecycle management with QR code generation
- **Automated Content**: Daily guides auto-generated and updated at 00:01 AM Sri Lankan time
- **Birth Chart Management**: Unified horoscope management with real astrological calculations
- **Analytics Dashboard**: Comprehensive user engagement and QR scan statistics
- **English-only Interface**: Admin panel remains in English for consistency

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL database
- Google Gemini API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd astroconnect
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/astroconnect"

   # Google Gemini API
   GEMINI_API_KEY="your_gemini_api_key"

   # Application
   NEXT_PUBLIC_APP_URL="http://localhost:3000"
   JWT_SECRET="your_jwt_secret_key"
   ```

4. **Set up the database**
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Push database schema
   npx prisma db push

   # (Optional) Seed sample data
   node scripts/populate-sample-data.js
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Architecture

```
astroconnect/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API endpoints
│   │   │   ├── auth/          # Authentication (QR & admin)
│   │   │   ├── dashboard/     # User dashboard data
│   │   │   ├── birth-chart/   # Birth chart calculations
│   │   │   ├── translate/     # Translation service
│   │   │   ├── admin/         # Admin operations
│   │   │   └── health/        # Health checks
│   │   ├── dashboard/         # User dashboard pages
│   │   ├── admin/             # Admin panel pages
│   │   ├── qr/                # QR authentication pages
│   │   └── globals.css        # Global styles
│   ├── components/            # React components
│   │   ├── admin/             # Admin-specific components
│   │   ├── QRScanner.tsx      # QR code scanner with dual modes
│   │   └── ui/                # Reusable UI components
│   ├── lib/                   # Core libraries
│   │   ├── prisma.ts          # Database client
│   │   ├── scheduler.ts       # Daily guide automation
│   │   ├── translation.ts     # Gemini API integration
│   │   └── auth.ts            # Authentication utilities
│   ├── types/                 # TypeScript definitions
│   ├── utils/                 # Utility functions
│   └── middleware.ts          # Next.js middleware
├── prisma/                    # Database schema and migrations
├── database/                  # SQL schema files
├── scripts/                   # Utility and deployment scripts
├── public/                    # Static assets
└── deploy-package-production/ # Production deployment package
```

## 🛠️ Technology Stack

### Frontend
- **Next.js 15**: React framework with App Router and server components
- **TypeScript**: Type-safe development with strict mode
- **TailwindCSS 4**: Utility-first CSS framework with custom purple theme
- **Framer Motion**: Smooth animations and page transitions
- **Lucide React**: Modern icon library with consistent design

### Backend & Database
- **Next.js API Routes**: Serverless API endpoints with middleware
- **Prisma ORM**: Type-safe database operations with PostgreSQL
- **PostgreSQL**: Robust relational database with advanced features
- **JWT Authentication**: Secure token-based authentication

### External Integrations
- **Google Gemini API**: AI-powered natural language translation
- **ZXing Library**: Comprehensive QR code scanning (camera + file upload)
- **Vedic Astrology Libraries**: Accurate birth chart calculations

### Development & Deployment
- **ESLint & TypeScript**: Code quality and type checking (disabled in build)
- **Jest**: Comprehensive unit testing framework
- **Docker**: Containerized deployment support
- **Vercel Ready**: Optimized for Vercel deployment

## � Feature Details

### QR Authentication System
- **Unique QR Codes**: Each user gets a permanent, cryptographically secure QR code
- **Dual Scanning Methods**: Camera scanning with native browser permissions + file upload
- **Mobile Optimized**: Works seamlessly on all mobile devices
- **Security**: Rate limiting and token validation

### Daily Guide Automation
- **AI-Generated Content**: Personalized daily guides using Gemini AI
- **Automated Scheduling**: Updates daily at 00:01 AM Sri Lankan time
- **Zodiac-based Personalization**: Content tailored to user's zodiac sign
- **Bilingual Storage**: Pre-translated content stored in database

### Birth Chart Analysis
- **Vedic Astrology**: Complete Lagna and Navamsa chart calculations
- **Visual Charts**: Beautiful, centered chart visualizations
- **Detailed Analysis**: Comprehensive astrological data and interpretations
- **Birth Location Support**: Accurate calculations using birth time and place

### Admin Management
- **Role Hierarchy**: Super Admin and Admin roles with appropriate permissions
- **User Lifecycle**: Complete user management from creation to deletion
- **Content Automation**: Automated daily guide generation and management
- **Analytics**: Detailed statistics and user engagement metrics

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Yes | `postgresql://user:pass@localhost:5432/db` |
| `GEMINI_API_KEY` | Google Gemini API key | Yes | `AIza...` |
| `NEXT_PUBLIC_APP_URL` | Application base URL | Yes | `https://yourdomain.com` |
| `JWT_SECRET` | JWT signing secret | Yes | `your-secret-key` |

### Database Schema
The application uses Prisma with PostgreSQL and includes:
- **Users**: Complete user profiles with birth data
- **QR Code Mappings**: Permanent QR token to user relationships
- **Birth Charts**: Detailed astrological chart data
- **Daily Zodiac Readings**: Automated daily guidance content
- **Translation Cache**: Optimized translation storage

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## 🚀 Deployment

### Vercel Deployment (Recommended)
See [DEPLOYMENT.md](./DEPLOYMENT.md) for comprehensive Vercel deployment instructions.

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Manual Server Deployment
```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🔒 Security Features

- **HTTPS Enforcement**: All traffic encrypted with security headers
- **Rate Limiting**: API protection against abuse and brute force attacks
- **Input Validation**: Comprehensive sanitization and validation
- **CORS Configuration**: Proper cross-origin resource sharing
- **Environment Protection**: Sensitive data secured with environment variables
- **Role-based Access**: Hierarchical admin permissions

## 🌐 API Endpoints

### Authentication
- `POST /api/auth/qr` - QR code authentication
- `POST /api/auth/admin` - Admin login

### User Dashboard
- `GET /api/dashboard` - Complete dashboard data
- `GET /api/birth-chart` - Birth chart analysis

### Admin Operations
- `GET /api/admin/users` - User management
- `GET /api/admin/stats` - Analytics dashboard
- `POST /api/admin/daily-guides/generate` - Manual guide generation

### Utilities
- `POST /api/translate` - Text translation
- `GET /api/health` - Application health check

## 🧪 Testing

```bash
# Run all tests
npm test

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage

# CI/CD testing
npm run test:ci
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## � License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support & Documentation

- **Issues**: Create GitHub issues for bugs and feature requests
- **Documentation**: Comprehensive guides in the `database/` folder
- **API Reference**: Complete endpoint documentation above
- **Deployment Guide**: Step-by-step instructions in [DEPLOYMENT.md](./DEPLOYMENT.md)

## � Version History

- **v1.0.0** - Initial release with QR authentication and basic horoscopes
- **v1.1.0** - Added bilingual support with Gemini AI translation
- **v1.2.0** - Enhanced QR scanning with dual camera/upload modes
- **v1.3.0** - Automated daily guide generation and scheduling
- **v1.4.0** - Complete birth chart analysis with Vedic astrology
- **v1.5.0** - Admin panel improvements and role hierarchy
- **v1.6.0** - Performance optimizations and Vercel deployment ready
