const { PrismaClient } = require('@prisma/client');
const { calculateBirthChart } = require('../src/lib/astrology');

async function testNavamsaCalculation() {
  console.log('🧪 Testing Navamsa calculation with specific Sri Lankan birth details...');
  console.log('📅 Birthday: 10/16/2024');
  console.log('⏰ Birth Time: 09:18 AM (Sri Lanka Time)');
  console.log('📍 Birth place: Peradeniya, Sri Lanka');
  console.log('🌍 Coordinates: 7.2667°N, 80.5913°E');

  try {
    const prisma = new PrismaClient();

    // Create or update test user with specific birth details
    const testUser = await prisma.user.upsert({
      where: { qrToken: 'navamsa-test-2024' },
      update: {
        name: 'Navamsa Test User',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra', // We'll verify this
        languagePreference: 'en'
      },
      create: {
        name: 'Navamsa Test User',
        email: '<EMAIL>',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthPlace: 'Peradeniya, Sri Lanka',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        zodiacSign: 'libra',
        languagePreference: 'en',
        qrToken: 'navamsa-test-2024'
      }
    });

    console.log('✅ Test user created/updated:', testUser.id);

    // Create QR code mapping if it doesn't exist
    await prisma.qrCodeMapping.upsert({
      where: { qrToken: 'navamsa-test-2024' },
      update: { userId: testUser.id },
      create: {
        qrToken: 'navamsa-test-2024',
        userId: testUser.id,
        scanCount: 0
      }
    });

    // Calculate birth chart with the exact details
    const birthDateTime = new Date('2024-10-16T09:18:00');
    const timezone = 'Asia/Colombo';

    console.log('\n🔄 Calculating birth chart with updated algorithms...');
    console.log('📊 Birth DateTime:', birthDateTime.toISOString());
    console.log('🕐 Timezone:', timezone);

    const chartData = await calculateBirthChart(
      birthDateTime,
      testUser.birthLatitude,
      testUser.birthLongitude,
      timezone
    );

    console.log('\n📈 CALCULATION RESULTS:');
    console.log('=' .repeat(50));
    console.log('🌅 Ascendant (Lagna):', chartData.ascendant);
    console.log('🌙 Moon Sign:', chartData.moonSign);
    console.log('☀️ Sun Sign:', chartData.sunSign);

    console.log('\n📊 NAVAMSA CHART ANALYSIS:');
    console.log('=' .repeat(50));
    if (chartData.navamsaChart && chartData.navamsaChart.houses) {
      console.log('🏠 Navamsa Houses:');
      chartData.navamsaChart.houses.forEach((house, index) => {
        console.log(`   House ${index + 1}: ${house.sign} - Planets: ${house.planets.map(p => p.name).join(', ') || 'None'}`);
      });

      // Check the first house sign (Navamsa Ascendant)
      const navamsaAscendant = chartData.navamsaChart.houses[0].sign;
      console.log(`\n🎯 NAVAMSA ASCENDANT: ${navamsaAscendant}`);
      console.log('📝 Expected from real horoscope: Sagittarius');
      console.log('🔍 Our calculation shows: ' + navamsaAscendant);
      
      if (navamsaAscendant === 'Sagittarius') {
        console.log('✅ NAVAMSA CALCULATION CORRECT!');
      } else {
        console.log('❌ NAVAMSA CALCULATION INCORRECT!');
        console.log('🔧 Need to fix the Navamsa calculation algorithm');
      }
    }

    console.log('\n📊 ASHTAKAVARGA ANALYSIS:');
    console.log('=' .repeat(50));
    if (chartData.ashtakavarga && chartData.ashtakavarga.sarvashtakavarga) {
      console.log('🎯 Sarvashtakavarga totals:');
      chartData.ashtakavarga.sarvashtakavarga.forEach((total, index) => {
        console.log(`   House ${index + 1}: ${total} points`);
      });
      
      const totalPoints = chartData.ashtakavarga.sarvashtakavarga.reduce((sum, points) => sum + points, 0);
      console.log(`\n📊 Total Ashtakavarga Points: ${totalPoints}`);
      console.log('📝 Expected range: 290-340 points (traditional range)');
      
      if (totalPoints >= 290 && totalPoints <= 340) {
        console.log('✅ ASHTAKAVARGA CALCULATION LOOKS REASONABLE!');
      } else {
        console.log('⚠️ ASHTAKAVARGA TOTALS OUTSIDE EXPECTED RANGE');
      }
    }

    // Save the birth chart to database
    console.log('\n💾 Saving birth chart to database...');
    const birthChart = await prisma.birthChart.upsert({
      where: { userId: testUser.id },
      update: {
        birthDateTime: birthDateTime,
        birthPlace: testUser.birthPlace,
        birthLatitude: testUser.birthLatitude,
        birthLongitude: testUser.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets,
        housePositions: chartData.houses,
        aspects: chartData.aspects,
        nakshatras: chartData.nakshatras,
        dashas: chartData.dashas,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        lagnaChart: chartData.lagnaChart,
        navamsaChart: chartData.navamsaChart,
        chandraChart: chartData.chandraChart,
        karakTable: chartData.karakTable,
        avasthaTable: chartData.avasthaTable,
        planetaryDetails: chartData.planetaryDetails,
        vimshottariDasha: chartData.vimshottariDasha,
        ashtakavarga: chartData.ashtakavarga,
        panchang: chartData.panchang,
        doshaAnalysis: chartData.doshaAnalysis,
        yogaAnalysis: chartData.yogaAnalysis,
        planetaryStrengths: chartData.planetaryStrengths,
        divisionalCharts: chartData.divisionalCharts,
        generalReading: chartData.generalReading,
        strengthsWeaknesses: chartData.strengthsWeaknesses,
        careerGuidance: chartData.careerGuidance,
        relationshipGuidance: chartData.relationshipGuidance,
        healthGuidance: chartData.healthGuidance,
        readingsEn: chartData.readingsEn,
        readingsSi: chartData.readingsSi,
        calculatedAt: new Date(),
        updatedAt: new Date()
      },
      create: {
        userId: testUser.id,
        birthDateTime: birthDateTime,
        birthPlace: testUser.birthPlace,
        birthLatitude: testUser.birthLatitude,
        birthLongitude: testUser.birthLongitude,
        timezone: timezone,
        planetPositions: chartData.planets,
        housePositions: chartData.houses,
        aspects: chartData.aspects,
        nakshatras: chartData.nakshatras,
        dashas: chartData.dashas,
        ascendant: chartData.ascendant,
        moonSign: chartData.moonSign,
        sunSign: chartData.sunSign,
        lagnaChart: chartData.lagnaChart,
        navamsaChart: chartData.navamsaChart,
        chandraChart: chartData.chandraChart,
        karakTable: chartData.karakTable,
        avasthaTable: chartData.avasthaTable,
        planetaryDetails: chartData.planetaryDetails,
        vimshottariDasha: chartData.vimshottariDasha,
        ashtakavarga: chartData.ashtakavarga,
        panchang: chartData.panchang,
        doshaAnalysis: chartData.doshaAnalysis,
        yogaAnalysis: chartData.yogaAnalysis,
        planetaryStrengths: chartData.planetaryStrengths,
        divisionalCharts: chartData.divisionalCharts,
        generalReading: chartData.generalReading,
        strengthsWeaknesses: chartData.strengthsWeaknesses,
        careerGuidance: chartData.careerGuidance,
        relationshipGuidance: chartData.relationshipGuidance,
        healthGuidance: chartData.healthGuidance,
        readingsEn: chartData.readingsEn,
        readingsSi: chartData.readingsSi,
        calculatedAt: new Date()
      }
    });

    console.log('✅ Birth chart saved with ID:', birthChart.id);

    console.log('\n🌐 TEST ACCESS:');
    console.log('=' .repeat(50));
    console.log('🔗 QR Token: navamsa-test-2024');
    console.log('🌐 Test URL: http://localhost:3000/auth?token=navamsa-test-2024');
    console.log('📱 Navigate to Horoscope tab to view the birth charts');

    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ Error in Navamsa calculation test:', error);
    throw error;
  }
}

// Run the test
testNavamsaCalculation()
  .then(() => {
    console.log('\n🎉 Navamsa calculation test completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
